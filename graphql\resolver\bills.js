const { subDays } = require("date-fns");
const Hotel = require("../../models/hotel");
const Table = require("../../models/table");
const { mongoBills, mongoSearchBill, mongoGetBill, mongoGenerateBill,
  mongoReGenerateBill, mongoDelBill, mongoFinalBills } = require("./mongo/billMongo");
const { setOneHotelRedis } = require("../../helpers/redis/hotel");
const { setAllTableRedis } = require("../../helpers/redis/table");
const { billDetail, ledgerDetail } = require("../../helpers/db");
const { ACTION_LIST, RESOURCE_LIST, isAuthorized } = require("../../helpers/permissionOptions");
const { customError } = require('../../helpers/customError');
const { stringToDbDate } = require("../../helpers/date");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { ACTIVITY_LIST } = require("../../helpers/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../helpers/messageOptions");
const FILE_NAME = "bill.js";

// get all documents of a collection
const bills = async (args, req) => {
  const FUNCTION_NAME = "bills";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.BILL)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    const allObjects = await mongoBills(hotelId, employeeId);

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BILL_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await billDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchBill = async (args, req) => {
  const FUNCTION_NAME = "searchBill";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { no, startDate, endDate } = args.billSearchInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.BILL)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    let billNo = null;
    (!no) ? billNo = "" : billNo = no.trim().toUpperCase();

    const startDateInput =
      startDate === ""
        ? new Date()
        : subDays(new Date(stringToDbDate(startDate)), 1).toISOString();

    const endDateInput =
      endDate === ""
        ? new Date()
        : subDays(new Date(stringToDbDate(endDate)), 1).toISOString();

    // get search data
    const searchObjects = await mongoSearchBill(hotelId, employeeId, billNo, startDateInput, endDateInput);

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BILL_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await billDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const getBill = async (args, req) => {
  const FUNCTION_NAME = "getBill";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.BILL)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // get data
    const getObject = await mongoGetBill(hotelId, employeeId, _id);
    if (!getObject) throw new customError(ERR_MSG.BILL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BILL_GET);

    // return output
    return await billDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const generateBill = async (args, req) => {
  const FUNCTION_NAME = "generateBill";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, type, attach } = args.billInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.CREATE, RESOURCE_LIST.BILL)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // generate bill
    const generateObject = await mongoGenerateBill(hotelId, employeeId, _id, type, attach);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      // update hotel
      const hotelCondition = { _id: hotelId, isEnable: true };
      const hotelCursor = await Hotel.findOne(hotelCondition);
      const hotelSpread = { ...hotelCursor._doc, _id: hotelCursor.id };
      await setOneHotelRedis(hotelId, hotelSpread);

      // update table
      const tableCondition = { hotel: hotelId, isEnable: true };
      const tableOrder = { no: 1 };
      const tableCursor = await Table.find(tableCondition).sort(tableOrder);
      const tableSpread = tableCursor.map((object) => {
        return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
      });
      await setAllTableRedis(hotelId, tableSpread);
    }

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BILL_GENERATE);

    // return output
    return await billDetail(generateObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const reGenerateBill = async (args, req) => {
  const FUNCTION_NAME = "reGenerateBill";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.EDIT, RESOURCE_LIST.BILL)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // re-generate bill
    const generateObject = await mongoReGenerateBill(hotelId, employeeId, _id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      // update hotel
      const hotelCondition = { _id: hotelId, isEnable: true };
      const hotelCursor = await Hotel.findOne(hotelCondition);
      const hotelSpread = { ...hotelCursor._doc, _id: hotelCursor.id };
      await setOneHotelRedis(hotelId, hotelSpread);

      // update table
      const tableCondition = { hotel: hotelId, isEnable: true };
      const tableOrder = { no: 1 };
      const tableCursor = await Table.find(tableCondition).sort(tableOrder);
      const tableSpread = tableCursor.map((object) => {
        return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
      });
      await setAllTableRedis(hotelId, tableSpread);
    }

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BILL_REGENERATE);

    // return output
    return await billDetail(generateObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delBill = async (args, req) => {
  const FUNCTION_NAME = "delBill";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.REMOVE, RESOURCE_LIST.BILL)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // delete data
    const delObject = await mongoDelBill(hotelId, employeeId, _id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      // update table
      const tableCursor = await Table.find({ hotel: hotelId, isEnable: true }).sort({ no: 1 });
      const tableSpread = tableCursor.map((object) => {
        return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
      });

      await setAllTableRedis(hotelId, tableSpread);
    }

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BILL_DEL);

    // return output
    return await billDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// get all documents of a collection
const finalBills = async (args, req) => {
  const FUNCTION_NAME = "finalBills";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { startDate, endDate } = args.billSearchInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.BILL)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    const startDateInput =
      startDate === ""
        ? new Date()
        : subDays(new Date(stringToDbDate(startDate)), 1).toISOString();

    const endDateInput =
      endDate === ""
        ? new Date()
        : new Date(stringToDbDate(endDate)).toISOString();

    // get all data
    const finalObjects = await mongoFinalBills(hotelId, employeeId, startDateInput, endDateInput);

    // return output
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BILL_FINAL_GET);

    // return output
    return finalObjects.map(async (object) => {
      return await ledgerDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { bills, searchBill, getBill, generateBill, reGenerateBill, delBill, finalBills };