const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../messageOptions");

// Function to get one payment mode data from Redis
const getOnePaymentModeRedis = async (hotelId, id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_PAYMENT_MODE}_${hotelId}:${id}`);

        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all payment mode data from Redis
const getAllPaymentModeRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_PAYMENT_MODE}_${hotelId}`;
        const pattern = '*';
        const hashKeys = await searchHashKeys(prefix, pattern);
        const parsedData = await Promise.all(hashKeys.map(
            async (hashKey) => {
                return await readHashValues(hashKey);
            })
        );
        const sortedByName = parsedData.sort((a, b) => a.name.localeCompare(b.name));

        return sortedByName;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set payment mode data in Redis
const setOnePaymentModeRedis = async (hotelId, data) => {
    try {
        if (data) {
            await delOnePaymentModeRedis(hotelId, data._id);

            await addHashValues(`${process.env.HASH_PAYMENT_MODE}_${hotelId}:${data._id}`,
                {
                    _id: data._id,
                    hotel: data.hotel,
                    name: data.name,
                    description: data.description,
                    isEnable: data.isEnable,
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt
                }
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set payment mode data in Redis
const setAllPaymentModeRedis = async (hotelId, dataArray) => {
    try {
        if (dataArray) {
            await delAllPaymentModeRedis(hotelId);

            dataArray.map(async (data) => {
                await addHashValues(`${process.env.HASH_PAYMENT_MODE}_${hotelId}:${data._id}`,
                    {
                        _id: data._id,
                        hotel: data.hotel,
                        name: data.name,
                        description: data.description,
                        isEnable: data.isEnable,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                )
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete payment mode data from Redis
const delOnePaymentModeRedis = async (hotelId, id) => {
    try {
        await delHashKey(`${process.env.HASH_PAYMENT_MODE}_${hotelId}:${id}`);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete payment mode data from Redis
const delAllPaymentModeRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_PAYMENT_MODE}_${hotelId}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);

        if (hashKeys) {
            await Promise.all(
                hashKeys.map(async (hashKey) => {
                    await delHashKey(hashKey)
                })
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

module.exports = {
    getOnePaymentModeRedis,
    getAllPaymentModeRedis,
    setOnePaymentModeRedis,
    setAllPaymentModeRedis,
    delOnePaymentModeRedis,
    delAllPaymentModeRedis
};
