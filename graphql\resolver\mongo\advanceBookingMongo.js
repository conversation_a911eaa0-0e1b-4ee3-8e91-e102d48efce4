const mongoose = require("mongoose");
const Room = require("../../../models/room");
const Guest = require("../../../models/guest");
const Booking = require("../../../models/booking");
const { GSTPercentageFind } = require("../../../helpers/db");
const { customError } = require('../../../helpers/customError');
const { writeErrLog } = require("../../../helpers/log");
const { frontEndToDbDate } = require("../../../helpers/date");
const { ROOM_STATUS } = require("../../../helpers/roomOptions");
const { ERR_CODE, ERR_MSG } = require("../../../helpers/messageOptions");
const FILE_NAME = "advanceBookingMongo.js";

// get all documents of a collection
const mongoAdvanceBookings = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "mongoAdvanceBookings";

  try {
    const condition = { hotel: hotelId, status: ROOM_STATUS.ADVANCE_BOOKED };
    const order = { startDate: 1, billNo: 1 };
    const cursor = await Booking.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        guest: object.guest.toString(),
        plan: object.plan.toString(),
        agent: object.agent.toString()
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const mongoSearchAdvanceBooking = async (hotelId, employeeId, startDate, endDate) => {
  const FUNCTION_NAME = "mongoSearchAdvanceBooking";

  try {
    const condition = {
      hotel: hotelId,
      startDate: { $gte: startDate, $lte: endDate },
      status: ROOM_STATUS.ADVANCE_BOOKED
    };
    const order = { startDate: 1, billNo: 1 };
    const cursor = await Booking.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        guest: object.guest.toString(),
        plan: object.plan.toString(),
        agent: object.agent.toString()
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const mongoGetAdvanceBooking = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoGetAdvanceBooking";
  let spread = null;

  try {
    const condition = { hotel: hotelId, _id: id };
    const cursor = await Booking.findOne(condition);

    // spread data
    if (cursor) spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      guest: cursor.guest.toString(),
      plan: cursor.plan.toString(),
      agent: cursor.agent.toString()
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const mongoAddAdvanceBooking = async (hotelId, employeeId, guestName, guestMobile, guestEmail, guestCount,
  company, companyAddress, companyGstNo, plan, agent, categories) => {
  const FUNCTION_NAME = "mongoAddAdvanceBooking";

  try {
    let occupancyDateArray = [];

    for (const category of categories) {
      const dt = new Date(frontEndToDbDate(category.occupancyDate));
      occupancyDateArray.push(dt);
    }

    const startDate = new Date(Math.min(...occupancyDateArray)).toISOString();
    const endDate = new Date(Math.max(...occupancyDateArray)).toISOString();
    const categoryIdSet = [...new Set(categories.map((object) => {
      return object.category;
    }))];

    // validate room
    const roomCondition = { hotel: hotelId, isEnable: true };
    const roomOrder = { category: 1, no: 1 };
    const objectArray = await Room.find(roomCondition).sort(roomOrder);
    const roomData = objectArray.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        category: object.category.toString()
      };
    });
    if (roomData.length === 0) throw new customError(ERR_MSG.ROOM_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // check availability of rooms from booked room of that category
    // Fetch all rooms booked during the requested period
    const pipeline = [
      {
        $match: {
          'hotel': new mongoose.Types.ObjectId(hotelId),
          status: { $eq: ROOM_STATUS.ADVANCE_BOOKED },
          $or: [{ startDate: { $lt: endDate } }, { endDate: { $gt: startDate } }]
        }
      },
      {
        $unwind: {
          path: '$rooms'
        }
      },
      {
        $sort: {
          occupancyDate: 1,
          room: 1
        }
      }
    ];

    const bookedRoomArray = await Booking.aggregate(pipeline).exec();

    // Extract room IDs that are booked
    const bookedRoomIdArray = bookedRoomArray.flatMap((roomObject) => {
      roomObject.rooms.map((object) => {
        return object.room.toString();
      })
    });

    // Fetch all rooms in the hotel and exclude the booked ones
    let availableRoomArray = [];

    if (bookedRoomIdArray.length > 0) {
      const bookedRoomSet = new Set(bookedRoomIdArray);
      availableRoomArray = roomData.filter((object) => {
        return !bookedRoomSet.has(object.room);
      });
    }
    else {
      availableRoomArray = [...roomData];
    }

    const categoryWiseAvailableRoomArray = availableRoomArray.filter((object) => {
      return categoryIdSet.includes(object.category.toString());
    });

    // validate no of available rooms for asking categories 
    let isError = false;

    for (const category of categories) {
      let emptyRoomArray = categoryWiseAvailableRoomArray.filter((object) => {
        if (category.category.toString() === object.category.toString()) {
          return category;
        }
      });

      if (emptyRoomArray.length < category.roomCount) isError = true;
    }
    if (isError) throw new customError(ERR_MSG.ROOM_NOT_AVAILABLE, ERR_CODE.NOT_EXISTS);

    // save data
    if (categoryWiseAvailableRoomArray.length > 0) {
      const roomsInput = [];
      const roomNoArray = [];
      let roomNoString = "";
      let dueAmount = 0;

      for (const category of categories) {
        const dt = new Date(frontEndToDbDate(category.occupancyDate));

        for (let index = 0; index < category.roomCount; index++) {
          const getRoomOfCategory = (categoryId, availableRooms, position) => {
            const roomData = availableRooms.filter((index) => {
              if (index.category.toString() === categoryId) {
                return index;
              }
            });

            let roomNo = null;

            if (roomData.length >= position) {
              roomNo = roomData[position - 1]
            }

            return roomNo;
          };

          const roomObject = getRoomOfCategory(category.category, categoryWiseAvailableRoomArray, index + 1);

          roomNoArray.push(roomObject.no);
          const { iGSTPercentage, sGSTPercentage } = await GSTPercentageFind(Number(parseFloat(roomObject.tariff).toFixed(2)) - Number(parseFloat(roomObject.maxDiscount).toFixed(2)));
          const iGSTAmount = ((Number(parseFloat(category.tariff).toFixed(2)) * Number(parseFloat(iGSTPercentage).toFixed(2))) / 100);
          const sGSTAmount = ((Number(parseFloat(category.tariff).toFixed(2)) * Number(parseFloat(sGSTPercentage).toFixed(2))) / 100);
          dueAmount = Number(parseFloat(dueAmount).toFixed(2)) +
            (Number(parseFloat(roomObject.tariff).toFixed(2)) -
              Number(parseFloat(roomObject.maxDiscount).toFixed(2))) +
            Number(parseFloat(iGSTAmount).toFixed(2)) +
            Number(parseFloat(sGSTAmount).toFixed(2));

          roomsInput.push({
            room: roomObject._id,
            guestCount: roomObject.accommodation,
            extraPersonCount: 0,
            extraBedCount: 0,
            discount: Number(parseFloat(roomObject.maxDiscount).toFixed(2)),
            tariff: Number(parseFloat(roomObject.tariff).toFixed(2)),
            iGSTPercentage: Number(parseFloat(iGSTPercentage).toFixed(2)),
            sGSTPercentage: Number(parseFloat(sGSTPercentage).toFixed(2)),
            iGSTAmount: Number(parseFloat(iGSTAmount).toFixed(2)),
            sGSTAmount: Number(parseFloat(sGSTAmount).toFixed(2)),
            occupancyDate: dt,
            actualAccommodation: roomObject.accommodation,
            actualTariff: (Number(parseFloat(roomObject.tariff).toFixed(2)) -
              Number(parseFloat(roomObject.maxDiscount).toFixed(2))).toFixed(0),
            actualExtraPersonTariff: 0,
            actualExtraBedTariff: 0,
            actualMaxDiscount: Number(parseFloat(roomObject.maxDiscount).toFixed(2)),
            breakfastGuestCount: roomObject.accommodation
          });
        }
      }

      const roomNoSet = [...new Set(roomNoArray)];
      roomNoSet.map((room) => {
        if (roomNoString.length > 0) roomNoString = roomNoString + "," + room;
        else roomNoString = room;
      });

      // add guest
      const addGuestData = new Guest({
        hotel: hotelId,
        employee: employeeId,
        name: guestName,
        mobile: guestMobile,
        email: guestEmail,
        guestCount: guestCount,
        company: company,
        companyAddress: companyAddress,
        companyGstNo: companyGstNo
      });
      const addGuestObject = await addGuestData.save();
      if (!addGuestObject) throw new customError(ERR_MSG.GUEST_NOT_SAVE, ERR_CODE.INTERNAL);

      // add booking
      const addBookingData = new Booking({
        hotel: hotelId,
        employees: employeeId,
        guest: addGuestObject._id,
        plan: plan,
        agent: agent,
        roomNos: roomNoString,
        startDate: startDate,
        endDate: endDate,
        rooms: roomsInput,
        dueAmount: Number(parseFloat(dueAmount).toFixed(0)),
        status: ROOM_STATUS.ADVANCE_BOOKED
      });
      const addBookingObject = await addBookingData.save();
      if (!addBookingObject) throw new customError(ERR_MSG.BOOKING_NOT_SAVE, ERR_CODE.INTERNAL);

      // find data
      const findCondition = { hotel: hotelId, _id: addBookingObject.id };
      const findCursor = await Booking.findOne(findCondition);
      const spread = {
        ...findCursor._doc,
        _id: findCursor.id,
        hotel: findCursor.hotel.toString(),
        guest: findCursor.guest.toString(),
        plan: findCursor.plan.toString(),
        agent: findCursor.agent.toString()
      };

      return spread;
    } else {
      throw new customError(ERR_MSG.ROOM_NOT_AVAILABLE, ERR_CODE.NOT_ALLOWED);
    }
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoModAdvanceBooking = async (hotelId, employeeId, id, guestName, guestMobile, guestEmail, guestCount,
  company, companyAddress, companyGstNo, plan, agent, categories) => {
  const FUNCTION_NAME = "mongoModAdvanceBooking";

  try {
    // validate booking
    const bookingCondition = { _id: id, status: ROOM_STATUS.ADVANCE_BOOKED };
    const bookingData = await Booking.findOne(bookingCondition);
    if (!bookingData) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    let dueAmount = Number(parseFloat(bookingData.dueAmount).toFixed(2));
    bookingData.rooms.map(async (object) => {
      dueAmount = Number(parseFloat(dueAmount).toFixed(2)) -
        (Number(parseFloat(object.tariff).toFixed(2)) +
          Number(parseFloat(object.iGSTAmount).toFixed(2)) +
          Number(parseFloat(object.sGSTAmount).toFixed(2)));

      await Room.findByIdAndUpdate(object.room, { status: ROOM_STATUS.EMPTY });
    });

    let occupancyDateArray = [];

    for (const category of categories) {
      const dt = new Date(frontEndToDbDate(category.occupancyDate));
      occupancyDateArray.push(dt);
    }

    const startDate = new Date(Math.min(...occupancyDateArray)).toISOString();
    const endDate = new Date(Math.max(...occupancyDateArray)).toISOString();
    const categoryIdSet = [...new Set(categories.map(object => object.category))];

    // validate guest
    const guestCondition = { _id: bookingData.guest, isEnable: true };
    const guestData = await Guest.findOne(guestCondition);
    if (!guestData) throw new customError(ERR_MSG.INVALID_GUEST, ERR_CODE.BAD_REQUEST);

    const guestId = guestData._id;
    const planInput = plan ? plan.trim() : bookingData.plan;
    const agentInput = agent ? agent.trim() : bookingData.agent;

    const roomCondition = { hotel: hotelId, isEnable: true };
    const roomOrder = { category: 1, no: 1 };
    const roomArray = await Room.find(roomCondition).sort(roomOrder);
    const roomData = roomArray.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        category: object.category.toString()
      };
    });
    if (roomData.length === 0) throw new customError(ERR_MSG.ROOM_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // check availability of rooms from booked room of that category
    // Fetch all rooms booked during the requested period
    const pipeline = [
      {
        $match: {
          'hotel': new mongoose.Types.ObjectId(hotelId),
          status: { $eq: ROOM_STATUS.ADVANCE_BOOKED },
          $or: [{ startDate: { $lt: endDate } }, { endDate: { $gt: startDate } }]
        }
      },
      {
        $unwind: {
          path: '$rooms'
        }
      },
      {
        $sort: {
          occupancyDate: 1,
          room: 1
        }
      }
    ];

    const bookedRoomArray = await Booking.aggregate(pipeline).exec();

    // Extract room IDs that are booked
    const bookedRoomIdArray = bookedRoomArray.flatMap((item) => {
      item.rooms.map((object) => {
        return object.room.toString();
      })
    });

    // Fetch all rooms in the hotel and exclude the booked ones
    let availableRoomArray = [];

    if (bookedRoomIdArray.length > 0) {
      const bookedRoomSet = new Set(bookedRoomIdArray);
      availableRoomArray = roomData.filter((object) => {
        return !bookedRoomSet.has(object.room)
      });
    }
    else {
      availableRoomArray = [...roomData];
    }

    const categoryWiseAvailableRoomArray = availableRoomArray.filter((object) => {
      return categoryIdSet.includes(object.category.toString());
    });

    // validate no of available rooms for asking categories 
    let isError = false;

    for (const category of categories) {
      let emptyRoomArray = categoryWiseAvailableRoomArray.filter((object) => {
        if (object.category.toString() === category.category.toString()) {
          return object;
        }
      });

      if (emptyRoomArray.length < category.roomCount) isError = true;
    }
    if (isError) throw new customError(ERR_MSG.ROOM_NOT_AVAILABLE, ERR_CODE.NOT_EXISTS);

    // save data
    if (categoryWiseAvailableRoomArray.length > 0) {
      const roomsInput = [];
      const roomNoArray = [];
      let roomNoString = "";
      let dueAmount = 0;

      for (const category of categories) {
        const dt = new Date(frontEndToDbDate(category.occupancyDate));

        for (let index = 0; index < category.roomCount; index++) {
          const getRoomOfCategory = (categoryId, availableRooms, position) => {
            const roomData = availableRooms.filter((index) => {
              if (index.category.toString() === categoryId) {
                return index;
              }
            });

            let roomNo = null;

            if (roomData.length >= position) {
              roomNo = roomData[position - 1]
            }

            return roomNo;
          };

          const roomObject = getRoomOfCategory(category.category, categoryWiseAvailableRoomArray, index + 1);
          roomNoArray.push(roomObject.no);
          const { iGSTPercentage, sGSTPercentage } = await GSTPercentageFind(Number(parseFloat(roomObject.tariff).toFixed(2)) - Number(parseFloat(roomObject.maxDiscount).toFixed(2)));
          const iGSTAmount = ((Number(parseFloat(category.tariff).toFixed(2)) * Number(parseFloat(iGSTPercentage).toFixed(2))) / 100);
          const sGSTAmount = ((Number(parseFloat(category.tariff).toFixed(2)) * Number(parseFloat(sGSTPercentage).toFixed(2))) / 100);
          dueAmount = Number(parseFloat(dueAmount).toFixed(2)) +
            (Number(parseFloat(roomObject.tariff).toFixed(2)) - Number(parseFloat(roomObject.maxDiscount).toFixed(2))) +
            Number(parseFloat(iGSTAmount).toFixed(2)) +
            Number(parseFloat(sGSTAmount).toFixed(2));

          roomsInput.push({
            room: roomObject._id,
            guestCount: roomObject.accommodation,
            extraPersonCount: 0,
            extraBedCount: 0,
            discount: Number(parseFloat(roomObject.maxDiscount).toFixed(2)),
            tariff: Number(parseFloat(roomObject.tariff).toFixed(2)),
            iGSTPercentage: Number(parseFloat(iGSTPercentage).toFixed(2)),
            sGSTPercentage: Number(parseFloat(sGSTPercentage).toFixed(2)),
            iGSTAmount: Number(parseFloat(iGSTAmount).toFixed(2)),
            sGSTAmount: Number(parseFloat(sGSTAmount).toFixed(2)),
            occupancyDate: dt,
            actualAccommodation: roomObject.accommodation,
            actualTariff: (Number(parseFloat(roomObject.tariff).toFixed(2)) - Number(parseFloat(roomObject.maxDiscount).toFixed(2))),
            actualExtraPersonTariff: 0,
            actualExtraBedTariff: 0,
            actualMaxDiscount: Number(parseFloat(roomObject.maxDiscount).toFixed(2)),
            breakfastGuestCount: roomObject.accommodation
          });
        }
      }

      const roomNoSet = [...new Set(roomNoArray)];
      roomNoSet.map((object) => {
        if (roomNoString.length > 0) roomNoString = roomNoString + ',' + object;
        else roomNoString = object;
      });

      // update guest
      const modGuestObject = await Guest.findByIdAndUpdate(guestId, {
        employees: employeeId,
        name: guestName,
        mobile: guestMobile,
        email: guestEmail,
        guestCount: guestCount,
        company: company,
        companyAddress: companyAddress,
        companyGstNo: companyGstNo
      });
      if (!modGuestObject) throw new customError(ERR_MSG.BOOKING_NOT_SAVE, ERR_CODE.INTERNAL);

      // update booking
      const modBookingObject = await Booking.findByIdAndUpdate(id, {
        employees: employeeId,
        plan: planInput,
        agent: agentInput,
        roomNos: roomNoString,
        startDate: startDate,
        endDate: endDate,
        dueAmount: dueAmount.toFixed(2),
        rooms: roomsInput,
        status: ROOM_STATUS.ADVANCE_BOOKED
      });
      if (!modBookingObject) throw new customError(ERR_MSG.BOOKING_NOT_SAVE, ERR_CODE.INTERNAL);

      // find data
      const findCondition = { hotel: hotelId, _id: modBookingObject.id };
      const findCursor = await Booking.findOne(findCondition);
      const spread = {
        ...findCursor._doc,
        _id: findCursor.id,
        hotel: findCursor.hotel.toString(),
        guest: findCursor.guest.toString(),
        plan: findCursor.plan.toString(),
        agent: findCursor.agent.toString()
      };

      return spread;
    } else {
      throw new customError(ERR_MSG.ROOM_NOT_AVAILABLE, ERR_CODE.NOT_ALLOWED);
    }
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const mongoDelAdvanceBooking = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoDelAdvanceBooking";

  try {
    // validate booking
    const bookingCondition = { _id: id, status: ROOM_STATUS.ADVANCE_BOOKED };
    const bookingCursor = await Booking.findOne(bookingCondition);
    const spread = {
      ...bookingCursor._doc,
      _id: bookingCursor.id,
      hotel: bookingCursor.hotel.toString(),
      guest: bookingCursor.guest.toString(),
      plan: bookingCursor.plan.toString(),
      agent: bookingCursor.agent.toString()
    };
    if (!bookingCursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // delete guest
    const guestCondition = { _id: spread.guest, status: ROOM_STATUS.ADVANCE_BOOKED };
    const delGuestObject = await Guest.deleteOne(guestCondition);
    if (!delGuestObject) throw new customError(ERR_MSG.BOOKING_NOT_DELETE, ERR_CODE.INTERNAL);

    // delete booking
    const delBookingCondition = { _id: id, status: ROOM_STATUS.ADVANCE_BOOKED };
    const delBookingObject = await Booking.deleteOne(delBookingCondition);
    if (!delBookingObject) throw new customError(ERR_MSG.BOOKING_NOT_DELETE, ERR_CODE.INTERNAL);

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = {
  mongoAdvanceBookings, mongoSearchAdvanceBooking, mongoGetAdvanceBooking,
  mongoAddAdvanceBooking, mongoModAdvanceBooking, mongoDelAdvanceBooking
};