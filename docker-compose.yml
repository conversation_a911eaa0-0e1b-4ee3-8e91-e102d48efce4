version: "3.9"
services:
  api:
    container_name: hotelapp_api
    build: .
    ports:
      - 4000
      - 4001
    environment:
      - APP_DOMAIN=http://43.239.110.22
      - APP_PORT=4000
      - APP_HTTPS_PORT_ALTERNATIVE=4001
      - APP_SSL_KEY_FILE=.//sslcert//private.key
      - APP_SSL_CERT_FILE=.//sslcert//certificate.crt
      - APP_LOG_FOLDER=log
      - APP_ACTIVITY_FOLDER=activity
      - APP_ERROR_FOLDER=error
      - APP_DB_BACKUP_PATH=.//db
      - APP_BACKUP_TIME=0 0 * * *
      - AI_ENABLED=false
      - OPENAI_API_KEY=********************************************************************************************************************************************************************
      - OPENAI_EMBEDDING_MODEL=text-embedding-ada-002
      - DB_MONGO_NAME=hotelApp
      - DB_MONGO_USER=hotelAppApi
      - DB_MONGO_PASSWORD=fyuRVVYI9vyNXq3d
      - DB_REDIS_ENABLED=true
      - DB_REDIS_HOST=redis-server-latest
      - DB_REDIS_PORT=6379
      - DB_REDIS_USER=default
      - DB_REDIS_PASSWORD=9h207XVNmfx36ruunf4QAwAHASRlyxne
      - DB_REDIS_SCAN_COUNT=100
      - DB_REDIS_EXPIRY_TIME=3600
      - DB_REDIS_REPOPULATE_TIME=*/50 * * * *
      - INDEX_GST_UNIQUE=gst_unique_index
      - INDEX_GST_FILTER=gst_filter_index
      - INDEX_ROLE_UNIQUE=role_unique_index
      - INDEX_ROLE_FILTER=role_filter_index
      - INDEX_ROLE_TEXT=role_text_index
      - INDEX_HOTEL_UNIQUE=hotel_unique_index
      - INDEX_HOTEL_FILTER=hotel_filter_index
      - INDEX_HOTEL_TEXT=hotel_text_index
      - INDEX_EMPLOYEE_UNIQUE=employee_unique_index
      - INDEX_EMPLOYEE_FILTER=employee_filter_index
      - INDEX_EMPLOYEE_TEXT=employee_text_index
      - INDEX_PLAN_UNIQUE=plan_unique_index
      - INDEX_PLAN_FILTER=plan_filter_index
      - INDEX_PLAN_TEXT=plan_text_index
      - INDEX_AGENT_UNIQUE=agent_unique_index
      - INDEX_AGENT_FILTER=agent_filter_index
      - INDEX_AGENT_TEXT=agent_text_index
      - INDEX_ID_CARD_UNIQUE=id_card_unique_index
      - INDEX_ID_CARD_FILTER=id_card_filter_index
      - INDEX_ID_CARD_TEXT=id_card_text_index
      - INDEX_PAYMENT_MODE_UNIQUE=payment_mode_unique_index
      - INDEX_PAYMENT_MODE_FILTER=payment_mode_filter_index
      - INDEX_PAYMENT_MODE_TEXT=payment_mode_text_index
      - INDEX_ROOM_CATEGORY_UNIQUE=room_category_unique_index
      - INDEX_ROOM_CATEGORY_FILTER=room_category_filter_index
      - INDEX_ROOM_CATEGORY_TEXT=room_category_text_index
      - INDEX_FOOD_UNIQUE=food_unique_index
      - INDEX_FOOD_FILTER=food_filter_index
      - INDEX_FOOD_TEXT=food_text_index
      - INDEX_SERVICE_UNIQUE=service_unique_index
      - INDEX_SERVICE_FILTER=service_filter_index
      - INDEX_SERVICE_TEXT=service_text_index
      - INDEX_MISCELLANEOUS_UNIQUE=miscellaneous_unique_index
      - INDEX_MISCELLANEOUS_FILTER=miscellaneous_filter_index
      - INDEX_MISCELLANEOUS_TEXT=miscellaneous_text_index
      - INDEX_ROOM_UNIQUE=room_unique_index
      - INDEX_ROOM_HOTEL_FILTER=room_filter_index
      - INDEX_ROOM_TEXT=room_text_index
      - INDEX_TABLE_UNIQUE=table_unique_index
      - INDEX_TABLE_FILTER=table_filter_index
      - INDEX_TABLE_TEXT=table_text_index
      - JWT_TOKEN_SECRET=79929dab59c07b6b8a82b4d2805f725b65eb5cf5d7a55733d6fb4cf46bd589585ee120568a6d972019ac303e1e19268a4b5b8c9679da3ced537f64f764185406
      - JWT_REFRESH_TOKEN_SECRET=dded652ea31c8a34d294a24d715e79a9d44e74cb9c17fba822766c3408db046faa89ab8c94bbd67c1929f332c391eeba29016ea85355367e6860abaaed26d637
      - JWT_TOKEN_EXPIRES=1h
      - JWT_REFRESH_TOKEN_EXPIRES=10m
      - OTP_LENGTH=6
      - OTP_DIGITS=0123456789
      - SMS_HOST=2factor.in
      - SMS_PORT=443
      - SMS_PATH=/API/R1/
      - SMS_METHOD=POST
      - SMS_MODULE=TRANS_SMS
      - SMS_TEMPLATEID=WBPNRD
      - SMS_APIKEY=7724f759-3a60-11ed-9c12-0200cd936042
      - EMAIL_HOST=smtp.gmail.com
      - EMAIL_PORT=587
      - EMAIL_ADDRESS=<EMAIL>
      - EMAIL_PASSWORD=Snowy@72101
      - HASH_ROLE=HashRole
      - HASH_GST=HastGST
      - HASH_HOTEL=HashHotel
      - HASH_AGENT=HashAgent
      - HASH_PLAN=HashPlan
      - HASH_IDENTIFICATION=HashIdentification
      - HASH_PAYMENT_MODE=HashPaymentMode
      - HASH_ROOM_CATEGORY=HashRoomCategory
      - HASH_FOOD=HashFood
      - HASH_SERVICE=HashService
      - HASH_MISCELLANEOUS=HashMiscellaneous
      - HASH_EMPLOYEE=HashEmployee
      - HASH_TABLE=HashTable
      - HASH_ROOM=HashRoom
      - HASH_Booking=HashBooking
      - HASH_GUEST=HashGuest
      - HASH_ORDER=HashOrder
      - HASH_BILL=HashBill
      - HASH_PAYMENT=HashPayment
    mem_limit: 512m # Set 512 MB RAM limit
    cpus: 0.5 # Set 0.5 vCPU limit
