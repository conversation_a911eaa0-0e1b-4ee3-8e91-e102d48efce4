const permissionOptions = [
    { module: "HOTEL", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "GST", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "ROL<PERSON>", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "PLAN", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "AGENT", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "ID_CARD", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "PAYMENT_MODE", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "ROOM_CATEGORY", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "EMPLOYEE", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "ROOM", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "TABLE", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "FOOD", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "SERVICE", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "MISCELLANEOUS", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "ADVANCE_BOOKING", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "BOOKING", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "FOOD_ORDER", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "SERVICE_ORDER", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "MISCELLANEOUS_ORDER", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "BREAKFAST", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "BILL", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "PAYMENT", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "REPORT_BREAKFAST", operations: ["VIEW", "EXPORT", "PRINT"] },
    { module: "REPORT_COLLECTION", operations: ["VIEW", "EXPORT", "PRINT"] },
    { module: "REPORT_SALE", operations: ["VIEW", "EXPORT", "PRINT"] },
    { module: "REPORT_CHECKOUT", operations: ["VIEW", "EXPORT", "PRINT"] },
    { module: "REPORT_POLICE", operations: ["VIEW", "EXPORT", "PRINT"] },
    { module: "ACTIVITY_LOG", operations: ["VIEW", "REMOVE"] },
    { module: "ERROR_LOG", operations: ["VIEW", "REMOVE"] },
    { module: "YEAR_END", operations: ["RUN"] },
    { module: "DB_BACKUP", operations: ["RUN"] }
];


module.exports = permissionOptions;