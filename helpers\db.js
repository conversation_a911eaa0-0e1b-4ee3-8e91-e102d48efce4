const mongoose = require("mongoose");
const GST = require("../models/gst");
const Role = require("../models/role");
const Hotel = require("../models/hotel");
const Employee = require("../models/employee");
const Agent = require("../models/agent");
const Plan = require("../models/plan");
const Identification = require("../models/identification");
const PaymentMode = require("../models/paymentMode");
const RoomCategory = require("../models/roomCategory");
const Food = require("../models/food");
const Service = require("../models/service");
const Miscellaneous = require("../models/miscellaneous");
const Room = require("../models/room");
const Table = require("../models/table");
const Guest = require("../models/guest");
const Booking = require("../models/booking");
const Order = require("../models/order");
const Bill = require("../models/bill");
const { getOneRoleRedis, getAllRoleRedis } = require("./redis/role");
const { getOneHotelRedis } = require("./redis/hotel");
const { getOneAgentRedis } = require("./redis/agent");
const { getOnePlanRedis } = require("./redis/plan");
const { getOneIdentificationRedis } = require("./redis/identification");
const { getOnePaymentModeRedis } = require("./redis/paymentMode");
const { getOneRoomCategoryRedis } = require("./redis/roomCategory");
const { getOneEmployeeRedis } = require("./redis/employee");
const { getOneTableRedis } = require("./redis/table");
const { getOneRoomRedis } = require("./redis/room");
const { getOneFoodRedis } = require("./redis/food");
const { getOneServiceRedis } = require("./redis/service");
const { getOneMiscellaneousRedis } = require("./redis/miscellaneous");
const { dateToString, isSameDay } = require("./date");
const { stringToBoolean } = require("./boolean");
const { ORDER_TYPE } = require("./orderOptions");
const { ERR_CODE, ERR_MSG } = require("../helpers/messageOptions");


const GSTPercentageFind = async (tariff) => {
  const object = await GST.findOne({
    $and: [{ minTariff: { $lte: tariff } }, { maxTariff: { $gte: tariff } }],
  });

  if (object) {
    return {
      iGSTPercentage: object.iGSTPercentage,
      sGSTPercentage: object.sGSTPercentage
    };
  } else {
    return {
      iGSTPercentage: 0,
      sGSTPercentage: 0
    };
  }
};

const roleFind = async (objectId) => {
  let spread = Object;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: objectId, isEnable: true };
      const cursor = await Role.findOne(condition);
      spread = { ...cursor._doc, _id: cursor.id };
    }
    else {
      spread = await getOneRoleRedis(objectId);
    }

    if (!spread) return null;

    return {
      _id: spread._id,
      name: spread.name,
      color: spread.color,
      description: spread.description,
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const hotelFind = async (objectId) => {
  let spread = null;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: objectId, isEnable: true };
      const cursor = await Hotel.findOne(condition);
      spread = { ...cursor._doc, _id: cursor.id };
    }
    else {
      spread = await getOneHotelRedis(objectId);
    }
    if (!spread) return null;

    return {
      _id: spread._id,
      name: spread.name,
      address: spread.address,
      city: spread.city,
      state: spread.state,
      pin: spread.pin,
      phone: spread.phone,
      email: spread.email,
      gstNo: spread.gstNo,
      foodIGSTPercentage: spread.foodIGSTPercentage,
      foodSGSTPercentage: spread.foodSGSTPercentage,
      serviceIGSTPercentage: spread.serviceIGSTPercentage,
      serviceSGSTPercentage: spread.serviceSGSTPercentage,
      serviceChargePercentage: spread.serviceChargePercentage,
      lastKOTNo: spread.lastKOTNo,
      lastSOTNo: spread.lastSOTNo,
      lastMOTNo: spread.lastMOTNo,
      lastReceiptNo: spread.lastReceiptNo,
      lastFoodBillNo: spread.lastFoodBillNo,
      lastFinalBillNo: spread.lastFinalBillNo
    };
  } catch (error) {
    throw error;
  }
};

const employeeFind = async (hotelId, objectId) => {
  let spread = null;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: objectId, hotel: hotelId, isEnable: true };
      const cursor = await Employee.findOne(condition);
      if (!cursor) return null;
      spread = {
        ...cursor._doc,
        _id: cursor.id,
        role: cursor.role.toString(),
        hotel: cursor.hotel.toString()
      };
    }
    else {
      spread = await getOneEmployeeRedis(hotelId, objectId);
    }
    if (!spread) return null;

    return {
      _id: spread._id,
      hotel: await hotelFind.bind(this, spread.hotel),
      role: await roleFind.bind(this, spread.role),
      name: spread.name,
      address: spread.address,
      mobile: spread.mobile,
      email: spread.email,
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const agentFind = async (hotelId, objectId) => {
  let spread = null;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: objectId, hotel: hotelId, isEnable: true };
      const cursor = await Agent.findOne(condition);
      spread = {
        ...cursor._doc,
        _id: cursor.id,
        hotel: cursor.hotel.toString()
      };
    }
    else {
      spread = await getOneAgentRedis(hotelId, objectId);
    }

    if (!spread) return null;

    return {
      _id: spread._id,
      hotel: await hotelFind.bind(this, spread.hotel.toString()),
      name: spread.name,
      description: spread.description,
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const planFind = async (hotelId, objectId) => {
  let spread = null;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: objectId, hotel: hotelId, isEnable: true };
      const cursor = await Plan.findOne(condition);
      spread = {
        ...cursor._doc,
        _id: cursor.id,
        hotel: cursor.hotel.toString()
      };
    }
    else {
      spread = await getOnePlanRedis(hotelId, objectId);
    }

    if (!spread) return null;

    return {
      _id: spread._id,
      hotel: await hotelFind.bind(this, spread.hotel),
      name: spread.name,
      description: spread.description,
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const identificationFind = async (hotelId, objectId) => {
  let spread = null;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: objectId, hotel: hotelId, isEnable: true };
      const cursor = await Identification.findOne(condition);
      spread = {
        ...cursor._doc,
        _id: cursor.id,
        hotel: cursor.hotel.toString()
      };
    }
    else {
      spread = await getOneIdentificationRedis(hotelId, objectId);
    }

    if (!spread) return null;

    return {
      _id: spread._id,
      hotel: await hotelFind.bind(this, spread.hotel),
      name: spread.name,
      description: spread.description,
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const paymentModeFind = async (hotelId, objectId) => {
  let spread = null;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: objectId, hotel: hotelId, isEnable: true };
      const cursor = await PaymentMode.findOne(condition);
      spread = {
        ...cursor._doc,
        _id: cursor.id,
        hotel: cursor.hotel.toString()
      };
    }
    else {
      spread = await getOnePaymentModeRedis(hotelId, objectId);
    }

    if (!spread) return null;

    return {
      _id: spread._id,
      hotel: await hotelFind.bind(this, spread.hotel),
      name: spread.name,
      description: spread.description,
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const roomCategoryFind = async (hotelId, objectId) => {
  let spread = null;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: objectId, hotel: hotelId, isEnable: true };
      const cursor = await RoomCategory.findOne(condition);
      spread = {
        ...cursor._doc,
        _id: cursor.id,
        hotel: cursor.hotel.toString()
      };
    }
    else {
      spread = await getOneRoomCategoryRedis(hotelId, objectId);
    }

    if (!spread) return null;

    return {
      _id: spread._id,
      hotel: await hotelFind.bind(this, spread.hotel.toString()),
      name: spread.name,
      description: spread.description,
      accommodation: spread.accommodation,
      tariff: spread.tariff,
      extraBedTariff: spread.extraBedTariff,
      extraPersonTariff: spread.extraPersonTariff,
      maxDiscount: spread.maxDiscount,
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const foodFind = async (hotelId, objectId) => {
  let spread = null;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: objectId, hotel: hotelId, isEnable: true };
      const cursor = await Food.findOne(condition);
      spread = {
        ...cursor._doc,
        _id: cursor.id,
        hotel: cursor.hotel.toString()
      };
    }
    else {
      spread = await getOneFoodRedis(hotelId, objectId);
    }

    if (!spread) return null;

    return {
      _id: spread._id,
      hotel: await hotelFind.bind(this, spread.hotel.toString()),
      name: spread.name,
      description: spread.description,
      unitPrice: spread.unitPrice,
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const serviceFind = async (hotelId, objectId) => {
  let spread = null;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: objectId, hotel: hotelId, isEnable: true };
      const cursor = await Service.findOne(condition);
      spread = {
        ...cursor._doc,
        _id: cursor.id,
        hotel: cursor.hotel.toString()
      };
    }
    else {
      spread = await getOneServiceRedis(hotelId, objectId);
    }

    if (!spread) return null;

    return {
      _id: spread._id,
      hotel: await hotelFind.bind(this, spread.hotel),
      name: spread.name,
      description: spread.description,
      unitPrice: spread.unitPrice,
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const miscellaneousFind = async (hotelId, objectId) => {
  let spread = null;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: objectId, hotel: hotelId, isEnable: true };
      const cursor = await Miscellaneous.findOne(condition);
      spread = {
        ...cursor._doc,
        _id: cursor.id,
        hotel: cursor.hotel.toString()
      };
    }
    else {
      spread = await getOneMiscellaneousRedis(hotelId, objectId);
    }

    if (!spread) return null;

    return {
      _id: spread._id,
      hotel: await hotelFind.bind(this, spread.hotel),
      name: spread.name,
      description: spread.description,
      unitPrice: spread.unitPrice,
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const tableFind = async (hotelId, objectId) => {
  let spread = null;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: objectId, hotel: hotelId, isEnable: true };
      const cursor = await Table.findOne(condition);
      spread = {
        ...cursor._doc,
        _id: cursor.id,
        hotel: cursor.hotel.toString()
      };
    }
    else {
      spread = await getOneTableRedis(hotelId, objectId);
    }

    if (!spread) return null;

    return {
      _id: spread._id,
      hotel: await hotelFind.bind(this, spread.hotel),
      no: spread.no,
      description: spread.description,
      accommodation: spread.accommodation,
      status: spread.status,
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const roomFind = async (hotelId, objectId) => {
  let spread = null;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: objectId, hotel: hotelId, isEnable: true };
      const cursor = await Room.findOne(condition);
      spread = {
        ...cursor._doc,
        _id: cursor.id,
        hotel: cursor.hotel.toString(),
        category: cursor.category.toString()
      };
    }
    else {
      spread = await getOneRoomRedis(hotelId, objectId);
    }

    if (!spread) return null;

    return {
      _id: spread._id,
      hotel: await hotelFind.bind(this, spread.hotel),
      category: await roomCategoryFind.bind(this, spread.hotel, spread.category),
      no: spread.no,
      accommodation: spread.accommodation,
      tariff: spread.tariff,
      extraBedTariff: spread.extraBedTariff,
      extraPersonTariff: spread.extraPersonTariff,
      maxDiscount: spread.maxDiscount,
      status: spread.status,
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const guestFind = async (hotelId, objectId) => {
  let spread = null;

  try {
    const condition = { _id: objectId, hotel: hotelId };
    const cursor = await Guest.findOne(condition);
    spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      identification: cursor.identification ? cursor.identification.toString() : null
    };
    if (!spread) return null;

    return {
      _id: spread._id,
      hotel: await hotelFind.bind(this, spread.hotel),
      employee: await employeeFind.bind(this, spread.hotel, spread.employee),
      identification: await identificationFind.bind(this, spread.hotel, spread.identification),
      name: spread.name,
      address: spread.address ? spread.address : "",
      city: spread.city ? spread.city : "",
      policeStation: spread.policeStation ? spread.policeStation : "",
      state: spread.state ? spread.state : "",
      pin: spread.pin ? spread.pin : "",
      mobile: spread.mobile,
      email: spread.email,
      father: spread.father ? spread.father : "",
      age: spread.age ? spread.age : 0,
      guestCount: spread.guestCount,
      maleCount: spread.maleCount,
      femaleCount: spread.femaleCount,
      idNo: spread.idNo,
      company: spread.company,
      companyAddress: spread.companyAddress,
      companyGstNo: spread.companyGstNo,
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const advanceGuestFind = async (hotelId, objectId) => {
  try {
    const condition = { _id: objectId, hotel: hotelId };
    const cursor = await Guest.findOne(condition);
    if (!cursor) return null;

    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
    };

    return {
      _id: spread._id,
      hotel: await hotelFind.bind(this, spread.hotel),
      employee: await employeeFind.bind(this, spread.hotel, spread.employee),
      name: spread.name,
      mobile: spread.mobile,
      email: spread.email,
      guestCount: spread.guestCount,
      maleCount: spread.maleCount,
      femaleCount: spread.femaleCount,
      company: spread.company,
      companyAddress: spread.companyAddress,
      companyGstNo: spread.companyGstNo,
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const bookingFind = async (hotelId, objectId) => {
  let spread = null;

  try {
    const condition = { _id: objectId, hotel: hotelId };
    const cursor = await Booking.findOne(condition);
    spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employees: cursor.employees.map((employee) => employee.toString()),
      guest: cursor.guest.toString(),
      plan: cursor.plan.toString(),
      agent: cursor.agent.toString()
    };
    if (!spread) return null;

    return {
      _id: spread._id,
      hotel: await hotelFind.bind(this, spread.hotel),
      employees: await assignedEmployeeList.bind(this, spread.hotel, spread.employees),
      guest: await guestFind.bind(this, spread.hotel, spread.guest),
      plan: await planFind.bind(this, spread.hotel, spread.plan),
      agent: await agentFind.bind(this, spread.hotel, spread.agent),
      rooms: await bookedRoomList.bind(this, spread.hotel, spread.rooms, null),
      roomNos: spread.roomNos,
      dueAmount: spread.dueAmount,
      billNo: spread.billNo,
      status: spread.status,
      startDate: dateToString(spread.startDate),
      endDate: dateToString(spread.endDate),
      createdAt: dateToString(spread.createdAt),
      updatedAt: dateToString(spread.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const orderFind = async (hotelId, objectId) => {
  try {
    const pipeline = [
      {
        $match: {
          '_id': new mongoose.Types.ObjectId(objectId),
          'hotel': new mongoose.Types.ObjectId(hotelId)
        }
      },
      { $unwind: '$tokens' },
      { $sort: { 'tokens.tokenNo': 1 } }
    ];
    const cursor = await Order.aggregate(pipeline).exec();
    if (cursor.length === 0) throw new Error(ERR_MSG.ORDER_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const spread = cursor.map((object) => {
      return {
        _id: object._id.toString(),
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        booking: object.booking ? object.booking.toString() : null,
        tables: object.tables ? object.tables.map((table) => table.toString()) : null,
        token: {
          _id: object.tokens._id.toString(),
          tokenNo: object.tokens.tokenNo,
          employee: object.tokens.employee.toString(),
          deliveryDate: object.tokens.deliveryDate,
          deliveryTime: object.tokens.deliveryTime,
          status: object.tokens.status,
          createdAt: object.createdAt,
          updatedAt: object.updatedAt,
          items: object.tokens.items.map(item => ({
            _id: item._id.toString(),
            item: item.item.toString(),
            name: item.name,
            unitPrice: item.unitPrice,
            quantity: item.quantity,
            status: item.status
          }))
        },
        type: object.type,
        status: object.status,
        createdAt: object.createdAt,
        updatedAt: object.updatedAt
      };
    });

    return spread.map(async (object) => {
      return await orderDetail(object);
    });
  } catch (error) {
    throw error;
  }
};

const orderFindByBookingId = async (hotelId, objectId) => {
  try {
    const pipeline = [
      {
        $match: {
          'hotel': new mongoose.Types.ObjectId(hotelId),
          'booking': new mongoose.Types.ObjectId(objectId)
        }
      },
      { $unwind: '$tokens' },
      { $sort: { 'tokens.tokenNo': 1 } }
    ];
    const cursor = await Order.aggregate(pipeline).exec();
    if (!cursor) throw new Error(ERR_MSG.ORDER_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const spread = cursor.map((object) => {
      return {
        _id: object._id.toString(),
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        booking: object.booking ? object.booking.toString() : null,
        tables: object.tables ? object.tables.map((table) => table.toString()) : null,
        token: {
          _id: object.tokens._id.toString(),
          tokenNo: object.tokens.tokenNo,
          employee: object.tokens.employee.toString(),
          deliveryDate: object.tokens.deliveryDate,
          deliveryTime: object.tokens.deliveryTime,
          status: object.tokens.status,
          createdAt: object.createdAt,
          updatedAt: object.updatedAt,
          items: object.tokens.items.map(item => ({
            _id: item._id.toString(),
            item: item.item.toString(),
            name: item.name,
            unitPrice: item.unitPrice,
            quantity: item.quantity,
            status: item.status
          }))
        },
        type: object.type,
        status: object.status,
        createdAt: object.createdAt,
        updatedAt: object.updatedAt
      };
    })

    if (spread) {
      return spread.map(async (object) => {
        return await orderDetail(object);
      });
    }
    else {
      null;
    }
  } catch (error) {
    throw error;
  }
};

const billFind = async (hotelId, objectId) => {
  try {
    const condition = { _id: objectId, hotel: hotelId };
    const cursor = await Bill.findOne(condition);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      booking: cursor.booking ? cursor.booking.toString() : null,
      tables: cursor.tables ? cursor.tables.map((table) => table.toString()) : null,
      order: cursor.order.toString()
    };
    if (!spread) return null;

    return {
      _id: cursor._id,
      hotel: await hotelFind.bind(this, cursor.hotel),
      employee: await employeeFind.bind(this, cursor.hotel, cursor.employee),
      booking: cursor.booking ? await bookingFind.bind(this, cursor.hotel, cursor.booking) : null,
      tables: cursor.tables ? await bookedTableList.bind(this, cursor.hotel, cursor.tables) : null,
      order: await orderFind.bind(this, cursor.hotel, cursor.order),
      no: cursor.no,
      totalAmount: cursor.totalAmount,
      igstCharge: cursor.igstCharge,
      sgstCharge: cursor.sgstCharge,
      serviceCharge: cursor.serviceCharge,
      billAmount: cursor.billAmount,
      type: cursor.type,
      status: cursor.status,
      createdAt: dateToString(cursor.createdAt),
      updatedAt: dateToString(cursor.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};


const permissionList = async (objectArray) => {
  try {
    if (!Array.isArray(objectArray)) objectArray = JSON.parse(objectArray);

    return objectArray.map(async (object) => {
      return {
        module: object.module,
        operations: object.operations.map(operation => {
          return operation
        })
      };
    });
  } catch (error) {
    throw error;
  }
};

const assignedRoleList = async (objectList) => {
  let spread = null;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const cursor = await Role.find({ isEnable: true });
      spread = cursor.map((object) => {
        return { ...object._doc, _id: object.id };
      });
    }
    else {
      spread = await getAllRoleRedis();
    }
    if (!spread) return null;

    const data = spread.filter(role => objectList.includes(role._id));

    return data.map((object) => {
      return roleDetail(object);
    });
  } catch (error) {
    throw error;
  }
};

const assignedEmployeeList = async (hotelId, objectArray) => {
  let spread = null;

  try {
    if (!Array.isArray(objectArray)) objectArray = JSON.parse(objectArray);

    const ids = [];
    objectArray.map((id) => { ids.push(new mongoose.Types.ObjectId(id.toString())) });

    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: { $in: ids }, hotel: hotelId, isEnable: true };
      const cursor = await Employee.find(condition);
      spread = cursor.map((object) => {
        return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
      });
    } else {
      spread = await Promise.all(
        objectArray.map(async (id) => {
          return await getOneEmployeeRedis(hotelId, id);
        })
      );
    }

    if (!spread) return null;

    return spread.map((object) => {
      return employeeDetail(object);
    });
  } catch (error) {
    throw error;
  }
};

const bookedRoomList = async (hotelId, objectArray, occupancyDate = null) => {
  try {
    if (!Array.isArray(objectArray)) objectArray = JSON.parse(objectArray);

    if (occupancyDate === null) {
      return objectArray.map(async (object) => {
        return {
          _id: object._id,
          room: await roomFind.bind(this, hotelId, object.room),
          guestCount: object.guestCount,
          extraPersonCount: object.extraPersonCount,
          extraBedCount: object.extraBedCount,
          discount: object.discount,
          tariff: object.tariff,
          iGSTPercentage: object.iGSTPercentage,
          sGSTPercentage: object.sGSTPercentage,
          iGSTAmount: object.iGSTAmount,
          sGSTAmount: object.sGSTAmount,
          occupancyDate: dateToString(object.occupancyDate),
          actualAccommodation: object.actualAccommodation,
          actualExtraPersonTariff: object.actualExtraPersonTariff,
          actualExtraBedTariff: object.actualExtraBedTariff,
          actualMaxDiscount: object.actualMaxDiscount,
          actualTariff: object.actualTariff,
          breakfastGuestCount: object.breakfastGuestCount,
          createdAt: dateToString(object.createdAt),
          updatedAt: dateToString(object.updatedAt)
        };
      });
    } else {
      return objectArray
        .filter((object) => isSameDay(occupancyDate, object.occupancyDate))
        .map(async (object) => ({
          _id: object._id,
          room: await roomFind.bind(this, hotelId, object.room),
          guestCount: object.guestCount,
          extraPersonCount: object.extraPersonCount,
          extraBedCount: object.extraBedCount,
          discount: object.discount,
          tariff: object.tariff,
          iGSTPercentage: object.iGSTPercentage,
          sGSTPercentage: object.sGSTPercentage,
          iGSTAmount: object.iGSTAmount,
          sGSTAmount: object.sGSTAmount,
          occupancyDate: dateToString(object.occupancyDate),
          actualAccommodation: object.actualAccommodation,
          actualExtraPersonTariff: object.actualExtraPersonTariff,
          actualExtraBedTariff: object.actualExtraBedTariff,
          actualMaxDiscount: object.actualMaxDiscount,
          actualTariff: object.actualTariff,
          breakfastGuestCount: object.breakfastGuestCount,
          createdAt: dateToString(object.createdAt),
          updatedAt: dateToString(object.updatedAt),
        }));
    }
  } catch (error) {
    throw error;
  }
};

const bookedTableList = async (hotelId, objectArray) => {
  if (!Array.isArray(objectArray)) objectArray = JSON.parse(objectArray);

  let spread = null;

  try {
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { hotel: hotelId, _id: { $in: objectArray }, isEnable: true };
      const cursor = await Table.find(condition);
      if (!cursor) return null;
      spread = cursor.map((object) => {
        return { ...object._doc, _id: object.id };
      });
    } else {
      spread = await Promise.all(
        objectArray.map(async (id) => {
          return await getOneTableRedis(hotelId, id);
        })
      );
      if (!spread) return null;
    }

    return spread.map(async (object) => {
      return await tableDetail(object);
    });
  } catch (error) {
    throw error;
  }
};

const orderedFoodList = async (objectArray) => {
  try {
    if (!Array.isArray(objectArray)) objectArray = JSON.parse(objectArray);

    return objectArray.map((object) => {
      return {
        _id: object._id,
        itemId: object.item,
        name: object.name,
        quantity: object.quantity,
        unitPrice: object.unitPrice,
        foodIGSTPercentage: object.foodIGSTPercentage,
        foodSGSTPercentage: object.foodSGSTPercentage,
        serviceChargePercentage: object.serviceChargePercentage,
        foodIGSTAmount: object.foodIGSTAmount,
        foodSGSTAmount: object.foodSGSTAmount,
        serviceChargeAmount: object.serviceChargeAmount,
        status: object.status,
        createdAt: dateToString(object.createdAt),
        updatedAt: dateToString(object.updatedAt)
      };
    });
  } catch (error) {
    throw error;
  }
};

const orderedServiceList = async (objectArray) => {
  try {
    if (!Array.isArray(objectArray)) objectArray = JSON.parse(objectArray);

    return objectArray.map((object) => {
      return {
        _id: object._id,
        itemId: object.item,
        name: object.name,
        quantity: object.quantity,
        unitPrice: object.unitPrice,
        foodIGSTPercentage: object.foodIGSTPercentage,
        foodSGSTPercentage: object.foodSGSTPercentage,
        serviceChargePercentage: object.serviceChargePercentage,
        foodIGSTAmount: object.foodIGSTAmount,
        foodSGSTAmount: object.foodSGSTAmount,
        serviceChargeAmount: object.serviceChargeAmount,
        status: object.status,
        createdAt: dateToString(object.createdAt),
        updatedAt: dateToString(object.updatedAt)
      };
    });
  } catch (error) {
    throw error;
  }
};

const orderedMiscellaneousList = async (objectArray) => {
  try {
    if (!Array.isArray(objectArray)) objectArray = JSON.parse(objectArray);

    return objectArray.map((object) => {
      return {
        _id: object._id,
        itemId: object.item,
        name: object.name,
        quantity: object.quantity,
        unitPrice: object.unitPrice,
        status: object.status,
        createdAt: dateToString(object.createdAt),
        updatedAt: dateToString(object.updatedAt)
      };
    });
  } catch (error) {
    throw error;
  }
};


const gstDetail = async (object) => {
  try {
    return {
      _id: object._id,
      minTariff: object.minTariff,
      maxTariff: object.maxTariff,
      iGSTPercentage: object.iGSTPercentage,
      sGSTPercentage: object.sGSTPercentage,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};


// const moduleDetail = async (object) => {
//   try {
//     return {
//       _id: object._id,
//       name: object.name,
//       permissions: object.permissions
//     };
//   } catch (error) {
//     throw error;
//   }
// };


const roleDetail = async (object) => {
  try {
    return {
      _id: object._id,
      name: object.name,
      color: object.color,
      permissions: await permissionList(object.permissions),
      description: object.description,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const hotelDetail = async (object) => {
  try {
    return {
      _id: object._id,
      name: object.name,
      address: object.address,
      city: object.city,
      state: object.state,
      pin: object.pin,
      phone: object.phone,
      email: object.email,
      gstNo: object.gstNo,
      urlName: object.urlName,
      urlPlace: object.urlPlace,
      foodIGSTPercentage: object.foodIGSTPercentage,
      foodSGSTPercentage: object.foodSGSTPercentage,
      serviceIGSTPercentage: object.serviceIGSTPercentage,
      serviceSGSTPercentage: object.serviceSGSTPercentage,
      serviceChargePercentage: object.serviceChargePercentage,
      lastKOTNo: object.lastKOTNo,
      lastSOTNo: object.lastSOTNo,
      lastMOTNo: object.lastMOTNo,
      lastReceiptNo: object.lastReceiptNo,
      lastFoodBillNo: object.lastFoodBillNo,
      lastFinalBillNo: object.lastFinalBillNo
    };
  } catch (error) {
    throw error;
  }
};

const agentDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      name: object.name,
      description: object.description,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const planDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      name: object.name,
      description: object.description,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const identificationDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      name: object.name,
      description: object.description,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const paymentModeDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      name: object.name,
      description: object.description,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const roomCategoryDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      name: object.name,
      accommodation: object.accommodation,
      tariff: object.tariff,
      extraBedTariff: object.extraBedTariff,
      extraPersonTariff: object.extraPersonTariff,
      maxDiscount: object.maxDiscount,
      description: object.description,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const foodDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      name: object.name,
      description: object.description,
      unitPrice: object.unitPrice,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const serviceDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      name: object.name,
      description: object.description,
      unitPrice: object.unitPrice,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const miscellaneousDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      name: object.name,
      description: object.description,
      unitPrice: object.unitPrice,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const tableDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      no: object.no,
      description: object.description,
      accommodation: object.accommodation,
      status: object.status,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const roomDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      category: await roomCategoryFind.bind(this, object.hotel, object.category),
      no: object.no,
      accommodation: object.accommodation,
      tariff: object.tariff,
      extraBedTariff: object.extraBedTariff,
      extraPersonTariff: object.extraPersonTariff,
      maxDiscount: object.maxDiscount,
      status: object.status,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const employeeDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      role: await roleFind.bind(this, object.role),
      name: object.name,
      address: object.address,
      mobile: object.mobile,
      email: object.email,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const guestDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      employee: await employeeFind.bind(this, object.hotel, object.employee),
      identification: await identificationFind.bind(this, object.hotel, object.identification),
      name: object.name,
      address: object.address,
      city: object.city,
      policeStation: object.policeStation,
      state: object.state,
      pin: object.pin,
      mobile: object.mobile,
      email: object.email,
      father: object.father,
      age: object.age,
      guestCount: object.guestCount,
      maleCount: object.maleCount,
      femaleCount: object.femaleCount,
      idNo: object.idNo,
      company: object.company,
      companyAddress: object.companyAddress,
      companyGstNo: object.companyGstNo,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const advanceBookingDetail = async (object) => {
  return {
    _id: object._id,
    hotel: await hotelFind.bind(this, object.hotel),
    employees: await assignedEmployeeList.bind(this, object.hotel, object.employees),
    guest: await advanceGuestFind.bind(this, object.hotel, object.guest),
    plan: await planFind.bind(this, object.hotel, object.plan),
    agent: await agentFind.bind(this, object.hotel, object.agent),
    rooms: await bookedRoomList.bind(this, object.hotel, object.rooms, null),
    roomNos: object.roomNos,
    dueAmount: object.dueAmount,
    billNo: object.billNo,
    status: object.status,
    startDate: dateToString(object.startDate),
    endDate: dateToString(object.endDate),
    createdAt: dateToString(object.createdAt),
    updatedAt: dateToString(object.updatedAt)
  };
};

const bookingDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      employees: await assignedEmployeeList.bind(this, object.hotel, object.employees),
      guest: await guestFind.bind(this, object.hotel, object.guest),
      plan: await planFind.bind(this, object.hotel, object.plan),
      agent: await agentFind.bind(this, object.hotel, object.agent),
      rooms: await bookedRoomList.bind(this, object.hotel, object.rooms, null),
      roomNos: object.roomNos,
      dueAmount: object.dueAmount,
      billNo: object.billNo ? object.billNo : "",
      status: object.status,
      startDate: dateToString(object.startDate),
      endDate: dateToString(object.endDate),
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const breakfastDetail = async (object, breakfastDate, guestCount, breakfastGuestCount) => {
  return {
    _id: object._id,
    hotel: await hotelFind.bind(this, object.hotel),
    employees: await employeeFind.bind(this, object.hotel, object.employees),
    guest: await guestFind.bind(this, object.hotel, object.guest),
    plan: await planFind.bind(this, object.hotel, object.plan),
    agent: await agentFind.bind(this, object.hotel, object.agent),
    rooms: await bookedRoomList.bind(this, object.hotel, object.rooms, breakfastDate),
    roomNos: object.roomNos,
    guestCount: guestCount,
    breakfastGuestCount: breakfastGuestCount,
    status: object.status,
    dueAmount: object.dueAmount.toFixed(2),
    billNo: object.billNo,
    startDate: dateToString(object.startDate),
    endDate: dateToString(object.endDate),
    breakfastDate: dateToString(breakfastDate),
    createdAt: dateToString(object.createdAt),
    updatedAt: dateToString(object.updatedAt)
  };
};

const orderDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      employees: await assignedEmployeeList.bind(this, object.hotel, object.employees),
      booking: object.booking ? await bookingFind.bind(this, object.hotel, object.booking) : null,
      tables: object.tables ? await bookedTableList.bind(this, object.hotel, object.tables) : null,
      token: await orderTokenDetail.bind(this, object.hotel, object.type, object.token),
      type: object.type,
      status: object.status,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const orderTokenDetail = async (hotelId, objectType, object) => {
  try {
    switch (objectType) {
      case ORDER_TYPE.FOOD:

        return {
          _id: object._id,
          tokenNo: object.tokenNo,
          status: object.status,
          employee: await employeeFind.bind(this, hotelId, object.employee),
          deliveryDate: object.deliveryDate,
          deliveryTime: object.deliveryTime,
          items: await orderedFoodList.bind(this, object.items),
          createdAt: dateToString(object.createdAt),
          updatedAt: dateToString(object.updatedAt)
        };

      case ORDER_TYPE.SERVICE:

        return {
          _id: object._id,
          tokenNo: object.tokenNo,
          status: object.status,
          employee: await employeeFind.bind(this, hotelId, object.employee),
          deliveryDate: object.deliveryDate,
          deliveryTime: object.deliveryTime,
          items: await orderedServiceList.bind(this, object.items),
          createdAt: dateToString(object.createdAt),
          updatedAt: dateToString(object.updatedAt)
        };

      case ORDER_TYPE.MISCELLANEOUS:

        return {
          _id: object._id,
          tokenNo: object.tokenNo,
          status: object.status,
          employee: await employeeFind.bind(this, hotelId, object.employee),
          deliveryDate: object.deliveryDate,
          deliveryTime: object.deliveryTime,
          items: await orderedMiscellaneousList.bind(this, object.items),
          createdAt: dateToString(object.createdAt),
          updatedAt: dateToString(object.updatedAt)
        };

      default:
        return null;
    };
  } catch (error) {
    throw error;
  }
};

const billDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      employee: await employeeFind.bind(this, object.hotel, object.employee),
      booking: object.booking ? await bookingFind.bind(this, object.hotel, object.booking) : null,
      tables: object.tables ? await bookedTableList.bind(this, object.hotel, object.tables) : null,
      order: await orderFind.bind(this, object.hotel, object.order),
      no: object.no,
      totalAmount: object.totalAmount,
      iGSTPercentage: object.iGSTPercentage,
      iGSTAmount: object.iGSTAmount,
      sGSTPercentage: object.sGSTPercentage,
      sGSTAmount: object.sGSTAmount,
      serviceChargeAmount: object.serviceChargeAmount,
      billAmount: object.billAmount,
      type: object.type,
      status: object.status,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const advancePaymentDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      employee: await employeeFind.bind(this, object.hotel, object.employee),
      mode: await paymentModeFind.bind(this, object.hotel, object.mode),
      receiptNo: object.receiptNo,
      amount: object.amount,
      particular: object.particular,
      date: dateToString(object.updatedAt),
    };
  } catch (error) {
    throw error;
  }
};

const attachedBillDetail = async (object) => {
  try {
    return {
      _id: object._id,
      date: object.createdAt.toISOString(),
      hotel: await hotelFind.bind(this, object.hotel),
      employee: await employeeFind.bind(this, object.hotel, object.employee),
      booking: object.booking ? await bookingFind.bind(this, object.hotel, object.booking) : null,
      tables: object.tables ? await bookedTableList.bind(this, object.hotel, object.tables) : null,
      orders: await orderFind.bind(this, object.hotel, object.order)
    };
  } catch (error) {
    throw error;
  }
};

const paymentDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      employee: await employeeFind.bind(this, object.hotel, object.employee),
      booking: object.booking ? await bookingFind.bind(this, object.hotel, object.booking) : null,
      bill: object.bill ? await billFind.bind(this, object.hotel, object.bill) : null,
      receiptNo: object.receiptNo,
      amount: object.amount,
      mode: await paymentModeFind.bind(this, object.hotel, object.mode),
      particular: object.particular,
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const ledgerDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      employees: await assignedEmployeeList.bind(this, object.hotel, object.employees),
      guest: await guestFind.bind(this, object.hotel, object.guest),
      plan: await planFind.bind(this, object.hotel, object.plan),
      agent: await agentFind.bind(this, object.hotel, object.agent),
      rooms: await bookedRoomList.bind(this, object.hotel, object.rooms, null),
      orders: await orderFindByBookingId.bind(this, object.hotel, object._id),
      roomNos: object.roomNos,
      billNo: object.billNo ? object.billNo : "",
      status: object.status,
      startDate: dateToString(object.startDate),
      endDate: dateToString(object.endDate)
    };
  } catch (error) {
    throw error;
  }
};

const roomSalesDetail = (object) => {
  try {
    return {
      occupancyDate: object.occupancyDate,
      room: object.room,
      tariff: object.tariff
    };
  } catch (error) {
    throw error;
  }
};

const checkoutDetail = async (object) => {
  try {
    return {
      _id: object._id,
      hotel: await hotelFind.bind(this, object.hotel),
      employees: await assignedEmployeeList.bind(this, object.hotel, object.employees),
      guest: await guestFind.bind(this, object.hotel, object.guest),
      rooms: await bookedRoomList.bind(this, object.hotel, object.rooms, null),
      plan: await planFind.bind(this, object.hotel, object.plan),
      agent: await agentFind.bind(this, object.hotel, object.agent),
      roomNos: object.roomNos,
      dueAmount: object.dueAmount,
      billNo: object.billNo ? object.billNo : "",
      status: object.status,
      startDate: dateToString(object.startDate),
      endDate: dateToString(object.endDate),
      createdAt: dateToString(object.createdAt),
      updatedAt: dateToString(object.updatedAt)
    };
  } catch (error) {
    throw error;
  }
};

const LogFileDetail = async (object) => {
  return {
    fileName: object.name,
    fileSize: (object.size / 1024).toFixed(2) + " KB",
    fileDate: dateToString(object.date)
  }
};

const ActivityLogDetail = async (object) => {
  try {
    return {
      dated: dateToString(object.timestamp),
      hotel: await hotelFind.bind(this, object.hotel),
      employee: await employeeFind.bind(this, object.hotel, object.employee),
      activity: object.activity,
    };
  } catch (error) {
    throw error;
  }
};

const ErrorLogDetail = async (object) => {
  try {
    return {
      dated: dateToString(object.timestamp),
      hotel: await hotelFind.bind(this, object.hotel),
      employee: await employeeFind.bind(this, object.hotel, object.employee),
      module: object.module,
      process: object.process,
      message: object.message,
    };
  } catch (error) {
    throw error;
  }
};


module.exports = {
  GSTPercentageFind, hotelFind, roleFind, employeeFind, agentFind, planFind,
  identificationFind, foodFind, serviceFind, miscellaneousFind, tableFind,
  roomCategoryFind, roomFind, paymentModeFind, guestFind, bookingFind,
  orderFindByBookingId, orderFind, billFind, permissionList, assignedRoleList, assignedEmployeeList,
  bookedTableList, bookedRoomList, gstDetail, hotelDetail, roleDetail, employeeDetail,
  agentDetail, identificationDetail, planDetail, foodDetail, serviceDetail, miscellaneousDetail,
  paymentModeDetail, roomCategoryDetail, tableDetail, roomDetail, guestDetail, advanceBookingDetail,
  bookingDetail, breakfastDetail, orderDetail, orderTokenDetail, orderedFoodList, orderedServiceList,
  orderedMiscellaneousList, billDetail, advancePaymentDetail, attachedBillDetail, paymentDetail,
  roomSalesDetail, checkoutDetail, ledgerDetail, LogFileDetail, ActivityLogDetail, ErrorLogDetail
};