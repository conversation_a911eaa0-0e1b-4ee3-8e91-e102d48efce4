const Agent = require("../../../models/agent");
const { customError } = require('../../../helpers/customError');
const { writeErrLog } = require("../../../helpers/log");
const { ERR_CODE, ERR_MSG } = require("../../../helpers/messageOptions");
const FILE_NAME = "agentMongo.js";

// Get all documents of a collection
const mongoAgents = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "mongoAgents";

  try {
    // read all document from db
    const condition = { hotel: hotelId, isEnable: true };
    const order = { name: 1 };
    const cursor = await Agent.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString(), hotel: object.hotel.toString() };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Search within collection
const mongoSearchAgent = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "mongoSearchAgent";
  const order = { name: 1 };
  let condition = null;

  try {
    if (!searchKey.trim()) {
      condition = { hotel: hotelId, isEnable: true };
    }
    else {
      condition = { hotel: hotelId, isEnable: true, $text: { $search: searchKey } };
    }

    const cursor = await Agent.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString(), hotel: object.hotel.toString() }
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const mongoGetAgent = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoGetAgent";
  let spread = null;

  try {
    // read single data
    const condition = { _id: id, hotel: hotelId, isEnable: true };
    const cursor = await Agent.findOne(condition);
    if (cursor) spread = { ...cursor._doc, _id: cursor.id.toString(), hotel: cursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Insert a document into the collection
const mongoAddAgent = async (hotelId, employeeId, name, description) => {
  const FUNCTION_NAME = "mongoAddAgent";

  try {
    // check for duplicate data in db
    const condition = { hotel: hotelId, name: name, isEnable: true };
    const duplicateCursor = await Agent.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.AGENT_CONFLICT, ERR_CODE.CONFLICT);

    // insert data in db
    const data = { hotel: hotelId, name: name, description: description };
    const addData = new Agent(data);
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.AGENT_NOT_SAVE, ERR_CODE.INTERNAL);

    // find data
    const findCondition = { hotel: hotelId, _id: addObject.id };
    const findCursor = await Agent.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), hotel: findCursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Modify a document of a collection
const mongoModAgent = async (hotelId, employeeId, id, name, description) => {
  const FUNCTION_NAME = "mongoModAgent";

  try {
    // check for duplicate data in db
    const condition = { _id: { $not: { $eq: id } }, hotel: hotelId, name: name, isEnable: true };
    const duplicateCursor = await Agent.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.AGENT_CONFLICT, ERR_CODE.CONFLICT);

    // change data in db
    const modData = { name: name, description: description };
    const modObject = await Agent.findByIdAndUpdate(id, modData, { new: true });
    if (!modObject) throw new customError(ERR_MSG.AGENT_NOT_SAVE, ERR_CODE.NOT_EXISTS);

    // find data
    const findCondition = { hotel: hotelId, _id: modObject.id };
    const findCursor = await Agent.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), hotel: findCursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const mongoDelAgent = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoDelAgent";

  try {
    // check for existence
    const condition = { _id: id, hotel: hotelId, isEnable: true };
    const cursor = await Agent.findOne(condition);
    const spread = { ...cursor._doc, _id: cursor.id.toString(), hotel: cursor.hotel.toString() };
    if (!spread) throw new customError(ERR_MSG.AGENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // delete from db
    const delObject = await Agent.findByIdAndUpdate(id, { isEnable: false });
    if (!delObject) throw new customError(ERR_MSG.AGENT_NOT_DELETE, ERR_CODE.NOT_ALLOWED);

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const mongoDelAgents = async (hotelId, employeeId, ids) => {
  const FUNCTION_NAME = "mongoDelAgents";

  try {
    // read all agents from db
    const condition = { _id: { $in: ids }, hotel: hotelId, isEnable: true };
    const cursor = await Agent.find(condition);
    if (cursor.length !== ids.length) throw new customError(ERR_MSG.AGENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString(), hotel: object.hotel.toString() };
    });

    // delete from db
    const delArray = await Agent.updateMany({ _id: { $in: ids } }, { isEnable: false });
    if (!delArray) throw new customError(ERR_MSG.AGENT_NOT_DELETE, ERR_CODE.INTERNAL);

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = {
  mongoAgents, mongoSearchAgent, mongoGetAgent, mongoAddAgent,
  mongoModAgent, mongoDelAgent, mongoDelAgents
};