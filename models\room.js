const mongoose = require("mongoose");
require("mongoose-double")(mongoose);
const Schema = mongoose.Schema;
const { ROOM_STATUS } = require("../helpers/roomOptions");

const roomSchema = new Schema(
  {
    hotel: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Hotel"
    },
    category: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "RoomCategory"
    },
    no: {
      type: String,
      required: true,
      trim: true,
      uppercase: true
    },
    accommodation: {
      type: Number,
      required: true,
      min: 1,
      max: 100,
      default: 1
    },
    tariff: {
      type: Number,
      required: true,
      min: 0
    },
    extraBedTariff: {
      type: Number,
      required: true,
      min: 0
    },
    extraPersonTariff: {
      type: Number,
      required: true,
      min: 0
    },
    maxDiscount: {
      type: Number,
      required: true,
      min: 0
    },
    status: {
      type: String,
      required: true,
      enum: [
        ROOM_STATUS.ADVANCE_BOOKED,
        ROOM_STATUS.BOOKED,
        ROOM_STATUS.READY,
        ROOM_STATUS.OCCUPIED,
        ROOM_STATUS.BILLED,
        // ROOM_STATUS.ATTACHED,
        ROOM_STATUS.PAID,
        ROOM_STATUS.EMPTY,
        ROOM_STATUS.CHECKED_OUT
      ],
      default: ROOM_STATUS.EMPTY
    },
    isEnable: {
      type: Boolean,
      default: true,
      required: true
    }
  },
  { timestamps: true }
);

module.exports = mongoose.model("Room", roomSchema);