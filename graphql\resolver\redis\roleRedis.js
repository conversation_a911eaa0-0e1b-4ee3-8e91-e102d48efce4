const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { setOneRoleRedis } = require("../../../helpers/redis/role");
const { customError } = require('../../../helpers/customError');
const { writeErrLog } = require("../../../helpers/log");
const FILE_NAME = "roleRedis.js";

// get all documents of a collection
const redisRoles = async () => {
  try {
    // get all data
    const query = `(@isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ROLE_FILTER}`,
      query,
      'RETURN', '5', '_id', 'name', 'color', 'permissions', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '20'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      return results;
    }

    return [];
  } catch (error) {
    throw new customError(error.message, error.code);
  }
};

// search within collection
const redisSearchRole = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "redisSearchRole";
  const regex = new RegExp(searchKey, "i");

  try {
    // get all data
    const query = `(@isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ROLE_FILTER}`,
      query,
      'RETURN', '5', '_id', 'name', 'color', 'permissions', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '20'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      if (!searchKey.trim()) {
        return results;
      }
      else {
        // filter array
        const filter = results.filter((item) => regex.test(item.name) ||
          regex.test(item.description));

        return filter;
      }
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const redisGetRole = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisGetRole";

  try {
    // read single data
    const query = `(@isEnable:{true} @_id:{${id}})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ROLE_UNIQUE}`,
      query,
      'RETURN', '5', '_id', 'name', 'color', 'permissions', 'description',
      'LIMIT', '0', '1'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      return results[0];
    }

    return {};
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisSetRole = async (hotelId, employeeId, data) => {
  const FUNCTION_NAME = "redisSetRole";

  try {
    // read single data
    await setOneRoleRedis(data);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisDelRole = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisDelRole";

  try {
    // delete single data
    const result = await redis.del(`${process.env.HASH_ROLE}:${id}`);

    return result;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { redisRoles, redisSearchRole, redisGetRole, redisSetRole, redisDelRole };