const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../messageOptions");

// Function to get one miscellaneous data from Redis
const getOneMiscellaneousRedis = async (hotelId, id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_MISCELLANEOUS}_${hotelId}:${id}`);
        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all miscellaneous data from Redis
const getAllMiscellaneousRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_MISCELLANEOUS}_${hotelId}`;
        const pattern = '*';
        const hashKeys = await searchHashKeys(prefix, pattern);
        const parsedData = await Promise.all(
            hashKeys.map(async (hashKey) => {
                return await readHashValues(hashKey);
            })
        );
        const sortedByName = parsedData.sort((a, b) => a.name.localeCompare(b.name));
        return sortedByName;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set miscellaneous data in Redis
const setOneMiscellaneousRedis = async (hotelId, data) => {
    try {
        if (data) {
            await delOneMiscellaneousRedis(hotelId, data._id);

            await addHashValues(`${process.env.HASH_MISCELLANEOUS}_${hotelId}:${data._id}`,
                {
                    _id: data._id,
                    hotel: data.hotel,
                    name: data.name,
                    unitPrice: data.unitPrice,
                    description: data.description,
                    isEnable: data.isEnable,
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt
                }
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set miscellaneous data in Redis
const setAllMiscellaneousRedis = async (hotelId, dataArray) => {
    try {
        if (dataArray) {
            await delAllMiscellaneousRedis(hotelId);

            dataArray.map(async (data) => {
                await addHashValues(`${process.env.HASH_MISCELLANEOUS}_${hotelId}:${data._id}`,
                    {
                        _id: data._id,
                        hotel: data.hotel,
                        name: data.name,
                        unitPrice: data.unitPrice,
                        description: data.description,
                        isEnable: data.isEnable,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                );
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete single data from Redis
const delOneMiscellaneousRedis = async (hotelId, id) => {
    try {
        await delHashKey(`${process.env.HASH_MISCELLANEOUS}_${hotelId}:${id}`)
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete miscellaneous data from Redis
const delAllMiscellaneousRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_MISCELLANEOUS}_${hotelId}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);

        if (hashKeys) await Promise.all(
            hashKeys.map(async (hashKey) => {
                await delHashKey(hashKey);
            })
        );
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

module.exports = { getOneMiscellaneousRedis, getAllMiscellaneousRedis, setOneMiscellaneousRedis, setAllMiscellaneousRedis, delOneMiscellaneousRedis, delAllMiscellaneousRedis };
