const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../messageOptions");

// Function to get role data from Redis
const getOneRoleRedis = async (id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_ROLE}:${id}`);

        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all role data from Redis
const getAllRoleRedis = async () => {
    try {
        const prefix = `${process.env.HASH_ROLE}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);
        const parsedData = await Promise.all(hashKeys.map(
            async (hashKey) => {
                return await readHashValues(hashKey);
            })
        );
        const sortedByName = parsedData.sort((a, b) => a.name.localeCompare(b.name));

        return sortedByName;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set role data in Redis
const setOneRoleRedis = async (data) => {
    try {
        if (data) {
            await delOneRoleRedis(data._id);
            await addHashValues(`${process.env.HASH_ROLE}:${data._id}`,
                {
                    _id: data._id,
                    hotel: data.hotel,
                    name: data.name,
                    color: data.color,
                    permissions: data.permissions,
                    description: data.description,
                    isEnable: data.isEnable,
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt
                }
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set role data in Redis
const setAllRoleRedis = async (dataArray) => {
    try {
        if (dataArray) {
            await delAllRoleRedis();

            dataArray.map(async (data) => {
                await addHashValues(`${process.env.HASH_ROLE}:${data._id}`,
                    {
                        _id: data._id,
                        name: data.name,
                        color: data.color,
                        permissions: JSON.stringify(data.permissions || []),
                        description: data.description,
                        isEnable: data.isEnable,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                );
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete single data from Redis
const delOneRoleRedis = async (id) => {
    try {
        await delHashKey(`${process.env.HASH_AGENT}:${id}`)
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete role data from Redis
const delAllRoleRedis = async () => {
    try {
        const prefix = `${process.env.HASH_ROLE}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);

        if (hashKeys) {
            await Promise.all(
                hashKeys.map(async (hashKey) => {
                    await delHashKey(hashKey);
                })
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

module.exports = {
    getOneRoleRedis,
    getAllRoleRedis,
    setOneRoleRedis,
    setAllRoleRedis,
    delOneRoleRedis,
    delAllRoleRedis
};