const Room = require("../../../models/room");
const RoomCategory = require("../../../models/roomCategory");
const { customError } = require('../../../helpers/customError');
const { writeErrLog } = require("../../../helpers/log");
const { ERR_CODE, ERR_MSG } = require("../../../helpers/messageOptions");
const FILE_NAME = "roomMongo.js";

// get all documents of a collection
const mongoRooms = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "mongoRooms";

  try {
    // read all document from db
    const condition = { hotel: hotelId, isEnable: true };
    const order = { no: 1, accommodation: 1, tariff: 1 };
    const cursor = await Room.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString(), hotel: object.hotel.toString() };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const mongoSearchRoom = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "mongoSearchRoom";
  const order = { no: 1 };
  let condition = null;

  try {
    if (!searchKey.trim()) {
      condition = { hotel: hotelId, isEnable: true };
    }
    else {
      condition = { hotel: hotelId, isEnable: true, $text: { $search: searchKey } };
    }

    const cursor = await Room.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString(), hotel: object.hotel.toString() }
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const mongoGetRoom = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoGetRoom";
  let spread = null;

  try {
    // read single data
    const condition = { _id: id, hotel: hotelId, isEnable: true };
    const cursor = await Room.findOne(condition);
    if (cursor) spread = { ...cursor._doc, _id: cursor.id.toString(), hotel: cursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const mongoAddRoom = async (hotelId, employeeId, category, no, accommodation, tariff,
  extraBedTariff, extraPersonTariff, maxDiscount) => {
  const FUNCTION_NAME = "mongoAddRoom";

  try {
    // check for duplicate data in db
    const condition = { hotel: hotelId, no: no, isEnable: true };
    const duplicateCursor = await Room.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.ROOM_CONFLICT, ERR_CODE.CONFLICT);

    // insert data in db
    const data = {
      hotel: hotelId,
      category: category,
      no: no,
      accommodation: accommodation,
      tariff: tariff,
      extraBedTariff: extraBedTariff,
      extraPersonTariff: extraPersonTariff,
      maxDiscount: maxDiscount
    };
    const addData = new Room(data);
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.AGENT_NOT_SAVE, ERR_CODE.INTERNAL);

    // find data
    const findCondition = { hotel: hotelId, _id: addObject.id };
    const findCursor = await Room.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), hotel: findCursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoModRoom = async (hotelId, employeeId, id, category, no, accommodation, tariff,
  extraBedTariff, extraPersonTariff, maxDiscount) => {
  const FUNCTION_NAME = "mongoModRoom";

  try {
    // check for category data in db
    const categoryCondition = { _id: category, hotel: hotelId, isEnable: true };
    const categoryCursor = await RoomCategory.findOne(categoryCondition);
    if (!categoryCursor) throw new customError(ERR_MSG.CATEGORY_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // check for duplicate data in db
    const condition = { _id: { $not: { $eq: id } }, hotel: hotelId, no: no, isEnable: true };
    const duplicateCursor = await Room.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.ROOM_CONFLICT, ERR_CODE.CONFLICT);

    // change data in db
    const modData = {
      category: category,
      no: no,
      accommodation: accommodation,
      tariff: tariff,
      extraBedTariff: extraBedTariff,
      extraPersonTariff: extraPersonTariff,
      maxDiscount: maxDiscount
    };
    const modObject = await Room.findByIdAndUpdate(id, modData, { new: true });
    if (!modObject) throw new customError(ERR_MSG.ROOM_NOT_SAVE, ERR_CODE.NOT_EXISTS);

    // find data
    const findCondition = { hotel: hotelId, _id: modObject.id };
    const findCursor = await Room.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), hotel: findCursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const mongoDelRoom = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoDelRoom";

  try {
    // check for existence
    const condition = { _id: id, hotel: hotelId, isEnable: true };
    const cursor = await Room.findOne(condition);
    const spread = { ...cursor._doc, _id: cursor.id.toString(), hotel: cursor.hotel.toString() };
    if (!spread) throw new customError(ERR_MSG.ROOM_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // delete from db
    const delObject = await Room.findByIdAndUpdate(id, { isEnable: false });
    if (!delObject) throw new customError(ERR_MSG.ROOM_NOT_DELETE, ERR_CODE.NOT_ALLOWED);

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const mongoDelRooms = async (hotelId, employeeId, ids) => {
  const FUNCTION_NAME = "mongoDelRooms";

  try {
    // read all agents from db
    const condition = { _id: { $in: ids }, hotel: hotelId, isEnable: true };
    const cursor = await Room.find(condition);
    if (cursor.length !== ids.length) throw new customError(ERR_MSG.ROOM_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString(), hotel: object.hotel.toString() };
    });

    // delete from db
    const delArray = await Room.updateMany({ _id: { $in: ids } }, { isEnable: false });
    if (!delArray) throw new customError(ERR_MSG.ROOM_NOT_DELETE, ERR_CODE.INTERNAL);

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = {
  mongoRooms, mongoSearchRoom, mongoGetRoom, mongoAddRoom,
  mongoModRoom, mongoDelRoom, mongoDelRooms
};