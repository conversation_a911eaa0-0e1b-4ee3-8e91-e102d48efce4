const { mongoGetRole } = require("../graphql/resolver/mongo/roleMongo");
const { redisGetRole } = require("../graphql/resolver/redis/roleRedis");
const { customError } = require('./customError');
const { stringToBoolean } = require("./boolean");
const { ERR_CODE, ERR_MSG } = require("./messageOptions");

const ROLE_LIST = {
    SYSTEM_ADMIN: "SYSTEM_ADMIN",
    HOTEL_ADMIN: "HOTEL_ADMIN",
    HOTEL_ACCOUNTANT: "HOTEL_ACCOUNTANT",
    RECEPTION_ADMIN: "RECEPTION_ADMIN",
    RECEPTION_STAFF: "RECEPTION_STAFF",
    RESTAURANT_ADMIN: "RESTAURANT_ADMIN",
    RESTAURANT_STAFF: "RESTAURANT_STAFF",
    KITCHEN_ADMIN: "KITCHEN_ADMIN",
    KIT<PERSON><PERSON>_STAFF: "KITCHEN_STAFF",
    HOUSEKEEPING_ADMIN: "HOUSEKEEPING_ADMIN",
    HOUSEKEEPING_STAFF: "HOUSEKEEPING_STAFF",
    VIEW_ONLY: "VIEW_ONLY",
    CURRENT_VIEW_ONLY: "CURRENT_VIEW_ONLY"
};

// find a single document by id from a collection
const getRoleName = async (hotelId, employeeId, id) => {
    let getObject = null;

    try {
        // validate inputs
        if (!id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

        // read single data
        if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) getObject = await mongoGetRole(hotelId, employeeId, id);
        else getObject = await redisGetRole(hotelId, employeeId, id);
        if (!getObject) throw new customError(ERR_MSG.ROLE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

        // return output
        return getObject.name;
    } catch (error) {
        throw new customError(error.message, error.code);
    }
};

module.exports = { ROLE_LIST, getRoleName };