const https = require("https");

const SMS_SETTINGS = {
    hostname: process.env.SMS_HOST,
    port: process.env.SMS_PORT,
    path: process.env.SMS_PATH,
    method: process.env.SMS_METHOD,
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
};

async function sendOtpSMS(to, otp) {
    try {
        const data = "module=" + process.env.SMS_METHOD +
            "&apikey=" + process.env.SMS_APIKEY +
            "&to=" + "91" + to +
            "&from=" + process.env.SMS_TEMPLATEID +
            "&msg=Your OTP for Banglar Para Baithak App login is " + otp + ". Please login using this OTP and do not share it with other. - P&RD Department -PRD Department";

        const req = https.request(SMS_SETTINGS, (res) => {
            res.on("data", (d) => {
                return true;
            });
        });

        req.on("error", (error) => {
            return false;
        });

        req.write(data);
        req.end();

        return true;
    } catch (e) {
        return false;
    }
};

module.exports = sendOtpSMS;