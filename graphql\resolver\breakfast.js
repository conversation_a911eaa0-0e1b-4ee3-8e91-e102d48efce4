const { mongoBreakfasts, mongoGetBreakfast, mongoModBreakfast } = require("./mongo/breakfastMongo");
const { breakfastDetail } = require("../../helpers/db");
const { ACTION_LIST, RESOURCE_LIST, isAuthorized } = require("../../helpers/permissionOptions");
const { customError } = require('../../helpers/customError');
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { ACTIVITY_LIST } = require("../../helpers/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../helpers/messageOptions");
const FILE_NAME = "breakfast.js";

// get all breakfast collection
const breakfasts = async (args, req) => {
  const FUNCTION_NAME = "breakfasts";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.BREAKFAST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    const allObjects = await mongoBreakfasts(hotelId, employeeId);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BREAKFAST_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await breakfastDetail(object.booking, object.currentDate,
        object.guestCount, object.breakfastGuestCount);
    });
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const getBreakfast = async (args, req) => {
  const FUNCTION_NAME = "getBreakfast";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.BREAKFAST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // get data
    const getObject = await mongoGetBreakfast(hotelId, employeeId, _id);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BREAKFAST_GET);

    // return output
    return await breakfastDetail(getObject.findBooking, getObject.currentDate,
      getObject.guestCount, getObject.breakfastGuestCount);
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modBreakfast = async (args, req) => {
  const FUNCTION_NAME = "modBreakfast";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, guestCount } = args.breakfastInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.EDIT, RESOURCE_LIST.BREAKFAST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (guestCount <= 0) throw new customError(ERR_MSG.INVALID_GUEST_COUNT, ERR_CODE.CONFLICT);

    // modify data
    const modObject = await mongoModBreakfast(hotelId, employeeId, _id, guestCount);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BREAKFAST_MOD);

    // return output
    return await breakfastDetail(modObject.booking, modObject.currentDate,
      modObject.totalGuest, modObject.guestCount);
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { breakfasts, getBreakfast, modBreakfast };