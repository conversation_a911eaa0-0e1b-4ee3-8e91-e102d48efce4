const Booking = require("../../models/booking");
const { ledgerDetail } = require("../../helpers/db");
const { ACTION_LIST, RESOURCE_LIST, isAuthorized } = require("../../helpers/permissionOptions");
const { customError } = require('../../helpers/customError');
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { ACTIVITY_LIST } = require("../../helpers/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../helpers/messageOptions");
const FILE_NAME = "ledger.js";

// find a document by id from a collection
const getLedger = async (args, req) => {
  const FUNCTION_NAME = "getLedger";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.LEDGER)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // get booking
    const getObject = await Booking.findOne({ _id: _id, hotel: hotelId });
    if (!getObject) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const spread = {
      ...getObject._doc,
      _id: getObject.id,
      hotel: getObject.hotel.toString(),
      employees: getObject.employees.map((employee) => employee.toString()),
      guest: getObject.guest.toString(),
      plan: getObject.plan.toString(),
      agent: getObject.agent.toString(),
      rooms: getObject.rooms.map((room) => {
        return {
          ...room._doc,
          _id: room.id,
          room: room.room.toString()
        };
      })
    };

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.LEDGER_GET);

    // return output
    return await ledgerDetail(spread);
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { getLedger };
