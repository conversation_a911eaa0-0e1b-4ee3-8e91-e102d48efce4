const Hotel = require("../../models/hotel");
const { ACTION_LIST, RESOURCE_LIST, isAuthorized } = require("../../helpers/permissionOptions");
const { customError } = require('../../helpers/customError');
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { ACTIVITY_LIST } = require("../../helpers/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../helpers/messageOptions");
const FILE_NAME = "yearEnd.js";

// year end process
const yearEnd = async (args, req) => {
  const FUNCTION_NAME = "yearEnd";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  try {
    // check authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.CREATE, RESOURCE_LIST.YEAR_END)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    const find = await Hotel.find({ _id: hotelId, isEnable: true });
    if (!find) throw new customError(ERR_MSG.HOTEL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    const updateData = {
      lastKOTNo: 0,
      lastSOTNo: 0,
      lastMOTNo: 0,
      lastFinalBillNo: 0,
      lastFoodBillNo: 0,
      lastServiceBillNo: 0,
      lastMiscellaneousBillNo: 0,
      lastReceiptNo: 0
    };

    const result = await Hotel.findByIdAndUpdate(hotelId, updateData);
    if (!result) throw new customError(ERR_MSG.HOTEL_NOT_SAVE, ERR_CODE.INTERNAL);

    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.YEAR_END);
    return true;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { yearEnd };