const { mongoTables, mongoSearchTable, mongoGetTable, mongoAddTable, mongoModTable, mongoDelTable, mongoDelTables } = require("./mongo/tableMongo");
const { redisTables, redisSearchTable, redisGetTable, redisSetTable, redisDelTable } = require("./redis/tableRedis");
const { tableDetail } = require("../../helpers/db");
const { ACTION_LIST, RESOURCE_LIST, isAuthorized } = require("../../helpers/permissionOptions");
const { customError } = require('../../helpers/customError');
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { ACTIVITY_LIST } = require("../../helpers/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../helpers/messageOptions");
const FILE_NAME = "tables.js";

// get all documents of a collection
const tables = async (args, req) => {
  const FUNCTION_NAME = "tables";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  let allObject = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.TABLE)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      allObject = await mongoTables(hotelId, employeeId);
    }
    else {
      allObject = await redisTables(hotelId, employeeId);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.TABLE_LIST);

    // return output
    return allObject.map(async (object) => {
      return await tableDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchTable = async (args, req) => {
  const FUNCTION_NAME = "searchTable";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.tableSearchInput ? args.tableSearchInput.trim().toUpperCase() : "";

  let searchObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.TABLE)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      searchObjects = await mongoSearchTable(hotelId, employeeId, searchKey);
    }
    else {
      searchObjects = await redisSearchTable(hotelId, employeeId, searchKey);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.TABLE_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await tableDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const getTable = async (args, req) => {
  const FUNCTION_NAME = "getTable";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  let getObject = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.TABLE)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      getObject = await mongoGetTable(hotelId, employeeId, _id);
    }
    else {
      getObject = await redisGetTable(hotelId, employeeId, _id);
    }

    if (!getObject) throw new customError(ERR_MSG.TABLE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.TABLE_GET);

    // return output
    return await tableDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addTable = async (args, req) => {
  const FUNCTION_NAME = "addTable";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { no, accommodation, description } = args.tableInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.CREATE, RESOURCE_LIST.TABLE)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (no === "") throw new customError(ERR_MSG.INVALID_NO, ERR_CODE.BAD_REQUEST);
    if (accommodation === "") throw new customError(ERR_MSG.INVALID_ACCOMMODATION, ERR_CODE.BAD_REQUEST);
    if (accommodation <= 0) throw new customError(ERR_MSG.INVALID_ACCOMMODATION, ERR_CODE.BAD_REQUEST);

    const _no = no.trim().toUpperCase();
    const _accommodation = accommodation;
    const _description = description ? description.trim() : "";

    // add data
    const addObject = await mongoAddTable(hotelId, employeeId, _no, _accommodation, _description);

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetTable(hotelId, employeeId, addObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.TABLE_ADD);

    // return output
    return await tableDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modTable = async (args, req) => {
  const FUNCTION_NAME = "modTable";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, no, accommodation, description } = args.tableInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.EDIT, RESOURCE_LIST.TABLE)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (_id === "") throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (no === "") throw new customError(ERR_MSG.INVALID_NO, ERR_CODE.BAD_REQUEST);
    if (accommodation === "") throw new customError(ERR_MSG.INVALID_ACCOMMODATION, ERR_CODE.BAD_REQUEST);
    if (accommodation <= 0) throw new customError(ERR_MSG.INVALID_ACCOMMODATION, ERR_CODE.BAD_REQUEST);

    const _no = no.trim().toUpperCase();
    const _accommodation = accommodation;
    const _description = description ? description.trim() : "";

    // modify data
    const modObject = await mongoModTable(hotelId, employeeId, _id, _no, _accommodation, _description);

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetTable(hotelId, employeeId, modObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.TABLE_MOD);

    // return output
    return await tableDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delTable = async (args, req) => {
  const FUNCTION_NAME = "delTable";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.REMOVE, RESOURCE_LIST.TABLE)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelTable(hotelId, employeeId, _id);

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) await redisDelTable(hotelId, employeeId, delObject._id);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.TABLE_DEL);

    // return output
    return await tableDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const delTables = async (args, req) => {
  const FUNCTION_NAME = "delTables";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _ids } = args.tablesInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.REMOVE, RESOURCE_LIST.TABLE)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_ids) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObjects = await mongoDelTables(hotelId, employeeId, _ids[0].split(","));

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      delObjects.map(async (object) => {
        await redisDelTable(hotelId, employeeId, object._id);
      });
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.TABLES_DEL);

    // return output
    return delObjects.map(async (object) => {
      return await tableDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { tables, searchTable, getTable, addTable, modTable, delTable, delTables };