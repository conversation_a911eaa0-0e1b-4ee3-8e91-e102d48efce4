const mongoose = require("mongoose");
const { TABLE_STATUS } = require("../helpers/tableOptions");
const Schema = mongoose.Schema;

const tableSchema = new Schema(
  {
    hotel: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Hotel"
    },
    no: {
      type: String,
      required: true,
      trim: true,
      uppercase: true
    },
    accommodation: {
      type: Number,
      required: true,
      default: 2
    },
    description: {
      type: String,
      trim: true
    },
    status: {
      type: String,
      required: true,
      enum: [
        TABLE_STATUS.READY,
        TABLE_STATUS.OCCUPIED,
        TABLE_STATUS.ORDERED,
        TABLE_STATUS.DELIVERED,
        TABLE_STATUS.BILLED,
        TABLE_STATUS.ATTACHED,
        TABLE_STATUS.PAID,
        TABLE_STATUS.EMPTY
      ],
      default: TABLE_STATUS.EMPTY
    },
    isEnable: {
      type: Boolean,
      default: true,
      required: true
    },
    embedding: {
      type: [Number],
      default: []
    }
  },
  { timestamps: true }
);

module.exports = mongoose.model("Table", tableSchema);