const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { setOneAgentRedis } = require("../../../helpers/redis/agent");
const { customError } = require('../../../helpers/customError');
const { writeErrLog } = require("../../../helpers/log");
const FILE_NAME = "agentRedis.js";

// Get all documents of a collection
const redisAgents = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "redisAgents";

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_AGENT_FILTER}`,
      query,
      'RETURN', '4', '_id', 'hotel', 'name', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '500'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      return results;
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Search within collection
const redisSearchAgent = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "redisSearchAgent";
  const regex = new RegExp(searchKey, "i");

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_AGENT_FILTER}`,
      query,
      'RETURN', '4', '_id', 'hotel', 'name', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '500'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      if (!searchKey.trim()) {
        return results;
      }
      else {
        // filter array
        const filter = results.filter((item) => regex.test(item.name) ||
          regex.test(item.description));

        return filter;
      }
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisGetAgent = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisGetAgent";

  try {
    // read single data
    const query = `(@hotel:{${hotelId}} @isEnable:{true} @_id:{${id}})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_AGENT_UNIQUE}`,
      query,
      'RETURN', '4', '_id', 'hotel', 'name', 'description',
      'LIMIT', '0', '1'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      return results[0];
    }

    return {};
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisSetAgent = async (hotelId, employeeId, data) => {
  const FUNCTION_NAME = "redisAddAgent";

  try {
    // read single data
    await setOneAgentRedis(hotelId, data);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisDelAgent = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisDelAgent";

  try {
    // delete single data
    const result = await redis.del(`${process.env.HASH_AGENT}_${hotelId}:${id}`);

    return result;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { redisAgents, redisSearchAgent, redisGetAgent, redisSetAgent, redisDelAgent };