const mongoose = require("mongoose");
require("mongoose-double")(mongoose);
const Schema = mongoose.Schema;

const hotelSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      uppercase: true
    },
    address: {
      type: String,
      required: true,
      trim: true,
      uppercase: true
    },
    city: {
      type: String,
      required: true,
      trim: true,
      uppercase: true
    },
    state: {
      type: String,
      required: true,
      trim: true,
      uppercase: true
    },
    pin: {
      type: String,
      required: true,
      trim: true
    },
    phone: {
      type: String
    },
    email: {
      type: String,
      required: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        "Invalid email!"
      ]
    },
    gstNo: {
      type: String
    },
    urlName: {
      type: String,
      required: true
    },
    urlPlace: {
      type: String,
      required: true
    },
    foodIGSTPercentage: {
      type: Number,
      default: 10.5
    },
    foodSGSTPercentage: {
      type: Number,
      default: 10.5
    },
    serviceIGSTPercentage: {
      type: Number,
      default: 2.5
    },
    serviceSGSTPercentage: {
      type: Number,
      default: 2.5
    },
    serviceChargePercentage: {
      type: Number,
      default: 12.5
    },
    lastKOTNo: {
      type: Number,
      default: 0
    },
    lastSOTNo: {
      type: Number,
      default: 0
    },
    lastMOTNo: {
      type: Number,
      default: 0
    },
    lastFinalBillNo: {
      type: Number,
      default: 0
    },
    lastFoodBillNo: {
      type: Number,
      default: 0
    },
    lastServiceBillNo: {
      type: Number,
      default: 0
    },
    lastMiscellaneousBillNo: {
      type: Number,
      default: 0
    },
    lastReceiptNo: {
      type: Number,
      default: 0
    },
    isEnable: {
      type: Boolean,
      default: true,
      required: true
    }
  },
  { timestamps: true }
);

module.exports = mongoose.model("Hotel", hotelSchema);