const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../messageOptions");

// Function to get one plan data from Redis
const getOnePlanRedis = async (hotelId, id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_PLAN}_${hotelId}:${id}`);

        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all plan data from Redis
const getAllPlanRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_PLAN}_${hotelId}`;
        const pattern = '*';
        const hashKeys = await searchHashKeys(prefix, pattern);
        const parsedData = await Promise.all(hashKeys.map(
            async (hashKey) => {
                return await readHashValues(hashKey);
            })
        );
        const sortedByName = parsedData.sort((a, b) => a.name.localeCompare(b.name));

        return sortedByName;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set plan data in Redis
const setOnePlanRedis = async (hotelId, data) => {
    try {
        if (data) {
            await delOnePlanRedis(hotelId, data._id);

            await addHashValues(`${process.env.HASH_PLAN}_${hotelId}:${data._id}`,
                {
                    _id: data._id,
                    hotel: data.hotel,
                    name: data.name,
                    description: data.description,
                    isEnable: data.isEnable,
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt
                }
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set plan data in Redis
const setAllPlanRedis = async (hotelId, dataArray) => {
    try {
        if (dataArray) {
            await delAllPlanRedis(hotelId);

            dataArray.map(async (data) => {
                await addHashValues(`${process.env.HASH_PLAN}_${hotelId}:${data._id}`,
                    {
                        _id: data._id,
                        hotel: data.hotel,
                        name: data.name,
                        description: data.description,
                        isEnable: data.isEnable,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                )
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete plan data from Redis
const delOnePlanRedis = async (hotelId, id) => {
    try {
        const hashKey = `${process.env.HASH_PLAN}_${hotelId}:${id}`;

        await delHashKey(hashKey);

        return true;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete plan data from Redis
const delAllPlanRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_PLAN}_${hotelId}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);

        if (hashKeys) {
            await Promise.all(
                hashKeys.map(async (hashKey) => {
                    await delHashKey(hashKey)
                })
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

module.exports = {
    getOnePlanRedis,
    getAllPlanRedis,
    setOnePlanRedis,
    setAllPlanRedis,
    delOnePlanRedis,
    delAllPlanRedis
};
