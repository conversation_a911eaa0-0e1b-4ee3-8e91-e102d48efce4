const Booking = require("../../../models/booking");
const { customError } = require('../../../helpers/customError');
const { writeErrLog } = require("../../../helpers/log");
const { ROOM_STATUS } = require("../../../helpers/roomOptions");
const FILE_NAME = "oldBookingMongo.js";

// Get all documents of a collection
const mongoOldBookings = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "mongoOldBookings";

  try {
    // read all document from db
    const condition = { hotel: hotelId, status: { $in: [ROOM_STATUS.CHECKED_OUT] } };
    const order = { startDate: -1 };
    const cursor = await Booking.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        guest: object.guest.toString(),
        plan: object.plan.toString(),
        agent: object.agent.toString(),
        roomNos: object.roomNos,
        rooms: object.rooms.map((room) => {
          return {
            ...room._doc,
            _id: room.id,
            room: room.room.toString()
          };
        }),
        startDate: object.startDate.toISOString(),
        endDate: object.endDate.toISOString(),
        billNo: object.billNo ? object.billNo : "",
        dueAmount: object.dueAmount,
        status: object.status,
        createdAt: object.createdAt.toISOString(),
        updatedAt: object.updatedAt.toISOString()
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const mongoSearchOldBooking = async (hotelId, employeeId, startDate, endDate) => {
  const FUNCTION_NAME = "mongoSearchOldBooking";

  try {
    const condition = {
      hotel: hotelId,
      status: { $in: [ROOM_STATUS.CHECKED_OUT] },
      startDate: { $gte: new Date(startDate), $lte: new Date(endDate) }
    };
    const order = { billNo: -1, startDate: -1 };
    const cursor = await Booking.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        guest: object.guest.toString(),
        plan: object.plan.toString(),
        agent: object.agent.toString(),
        rooms: object.rooms.map((room) => {
          return {
            ...room._doc,
            _id: room.id,
            room: room.room.toString()
          };
        })
      };
    });

    return spread;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { mongoOldBookings, mongoSearchOldBooking };