const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../messageOptions");

// Function to get one table data from Redis
const getOneTableRedis = async (hotelId, id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_TABLE}_${hotelId}:${id}`);
        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all table data from Redis
const getAllTableRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_TABLE}_${hotelId}`;
        const pattern = '*';
        const hashKeys = await searchHashKeys(prefix, pattern);
        const parsedData = await Promise.all(
            hashKeys.map(async (hashKey) => {
                return await readHashValues(hashKey);
            })
        );

        const sortedByNo = parsedData.sort((a, b) => a.no.localeCompare(b.no));
        return sortedByNo;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set table data in Redis
const setOneTableRedis = async (hotelId, data) => {
    try {
        if (data) {
            await delOneTableRedis(hotelId, data._id);

            // const vectorBuffer = data.embedding ? Buffer.from(new Float32Array(data.embedding).buffer) : Buffer.from([]);
            await addHashValues(`${process.env.HASH_TABLE}_${hotelId}:${data._id}`,
                {
                    _id: data._id,
                    hotel: data.hotel,
                    no: data.no,
                    accommodation: data.accommodation,
                    description: data.description,
                    status: data.status,
                    isEnable: data.isEnable,
                    // embedding: vectorBuffer,
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt
                }
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set table data in Redis
const setAllTableRedis = async (hotelId, dataArray) => {
    try {
        if (dataArray) {
            await delAllTableRedis(hotelId);

            dataArray.map(async (data) => {
                // const vectorBuffer = data.embedding ? Buffer.from(new Float32Array(data.embedding).buffer) : Buffer.from([]);
                await addHashValues(`${process.env.HASH_TABLE}_${hotelId}:${data._id}`,
                    {
                        _id: data._id,
                        hotel: data.hotel,
                        no: data.no,
                        accommodation: data.accommodation,
                        description: data.description,
                        status: data.status,
                        isEnable: data.isEnable,
                        // embedding: vectorBuffer,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                );
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete table data from Redis
const delOneTableRedis = async (hotelId, id) => {
    try {
        await delHashKey(`${process.env.HASH_TABLE}_${hotelId}:${id}`);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete table data from Redis
const delAllTableRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_TABLE}_${hotelId}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);

        if (hashKeys) {
            await Promise.all(
                hashKeys.map(async (hashKey) => {
                    await delHashKey(hashKey);
                })
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

module.exports = { getOneTableRedis, getAllTableRedis, setOneTableRedis, setAllTableRedis, delOneTableRedis, delAllTableRedis };