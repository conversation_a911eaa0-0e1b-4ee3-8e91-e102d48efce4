const Role = require("../../../models/role");
const { customError } = require('../../../helpers/customError');
const { writeErrLog } = require("../../../helpers/log");
const { ERR_CODE, ERR_MSG } = require("../../../helpers/messageOptions");
const FILE_NAME = "roleMongo.js";

// get all documents of a collection
const mongoRoles = async () => {
  try {
    // read all document from db
    const condition = { isEnable: true };
    const order = { name: 1 };
    const cursor = await Role.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString(), permissions: object.permissions };
    });

    return spread;
  } catch (error) {
    throw new customError(error.message, error.code);
  }
};

// search within collection
const mongoSearchRole = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "mongoSearchRole";
  const order = { name: 1 };
  let condition = null;

  try {
    if (!searchKey.trim()) {
      condition = { isEnable: true };
    }
    else {
      condition = { isEnable: true, $text: { $search: searchKey } };
    }

    const cursor = await Role.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString(), permissions: object.permissions }
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const mongoGetRole = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoGetRole";
  const optionsConfig = require('../../configs/permissionOptions');

  let spread = null;

  try {
    // read single data
    const condition = { _id: id, isEnable: true };
    const cursor = await Role.findOne(condition);
    if (cursor) spread = { ...cursor._doc, _id: cursor.id.toString() };
    // if (cursor) spread = { ...cursor._doc, _id: cursor.id.toString(), permissions: cursor.permissions };


    const permissionArray = optionsConfig.map(({ itemModuleConfig, operationsAllConfig }) => {

      const itemPermissionObject = {
        module: itemModuleConfig,
        operations: []
      };


      if (cursor.permissions.find((itemPermissionDB) => itemPermissionDB.module === itemModuleConfig)) {
        //   operationsAllConfig.map((itemOperationConfig) => {

        //     // const foundOperation = operationsAllConfig.find((itemPermissionConfig) => {

        //     // if (itemOperationConfig.includes(itemPermissionConfig)) {

        //     // } else {

        //     // }
        //   })

        // };

      }
      else {

        itemPermissionObject.operations = operationsAllConfig.map((itemOperationConfig) => {
          return {
            operation: itemOperationConfig,
            foundOperation: false
          };
        });


        return itemPermissionObject;
      }
    )

    // return spread;

    return permissionArray;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  } ``
};

// insert a document into the collection
const mongoAddRole = async (hotelId, employeeId, name, color, permissions, description) => {
  const FUNCTION_NAME = "mongoAddRole";

  try {
    // check for duplicate data in db
    const condition = { name: name, isEnable: true };
    const duplicateCursor = await Role.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.ROLE_CONFLICT, ERR_CODE.CONFLICT);

    // insert data in db
    const data = { name: name, color: color, permissions: permissions, description: description };
    const addData = new Role(data);
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.ROLE_NOT_SAVE, ERR_CODE.INTERNAL);

    // find data
    const findCondition = { _id: addObject.id };
    const findCursor = await Role.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), permissions: findCursor.permissions };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoModRole = async (hotelId, employeeId, id, name, color, permissions, description) => {
  const FUNCTION_NAME = "mongoModRole";

  try {
    // check for duplicate data in db
    const condition = { _id: { $not: { $eq: id } }, name: name, isEnable: true };
    const duplicateCursor = await Role.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.ROLE_CONFLICT, ERR_CODE.CONFLICT);

    // change data in db
    const modData = { name: name, color: color, permissions: permissions, description: description };
    const modObject = await Role.findByIdAndUpdate(id, modData, { new: true });
    if (!modObject) throw new customError(ERR_MSG.ROLE_NOT_SAVE, ERR_CODE.NOT_EXISTS);

    // find data
    const findCondition = { _id: modObject.id };
    const findCursor = await Role.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), permissions: findCursor.permissions };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const mongoDelRole = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoDelRole";

  try {
    // check for existence
    const condition = { _id: id, isEnable: true };
    const cursor = await Role.findOne(condition);
    const spread = { ...cursor._doc, _id: cursor.id.toString(), permissions: cursor.permissions };
    if (!spread) throw new customError(ERR_MSG.ROLE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // delete from db
    const delObject = await Role.findByIdAndUpdate(id, { isEnable: false });
    if (!delObject) throw new customError(ERR_MSG.ROLE_NOT_DELETE, ERR_CODE.NOT_ALLOWED);

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const mongoDelRoles = async (hotelId, employeeId, ids) => {
  const FUNCTION_NAME = "mongoDelRoles";

  try {
    // read all roles from db
    const condition = { _id: { $in: ids }, isEnable: true };
    const cursor = await Role.find(condition);
    if (cursor.length !== ids.length) throw new customError(ERR_MSG.ROLE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString(), permissions: object.permissions };
    });

    // delete from db
    const delArray = await Role.updateMany({ _id: { $in: ids } }, { isEnable: false });
    if (!delArray) throw new customError(ERR_MSG.ROLE_NOT_DELETE, ERR_CODE.INTERNAL);

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = {
  mongoRoles, mongoSearchRole, mongoGetRole, mongoAddRole,
  mongoModRole, mongoDelRole, mongoDelRoles
};