const { mongoRoles, mongoSearchRole, mongoGetRole, mongoAddRole, mongoModRole, mongoDelRole, mongoDelRoles } = require("./mongo/roleMongo");
const { redisRoles, redisSearchRole, redisGetRole, redisSetRole, redisDelRole } = require("./redis/roleRedis");
const { roleDetail } = require("../../helpers/db");
const { ACTION_LIST, RESOURCE_LIST, isAuthorized } = require("../../helpers/permissionOptions");
const { customError } = require('../../helpers/customError');
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { ACTIVITY_LIST } = require("../../helpers/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../helpers/messageOptions");
const FILE_NAME = "roles.js";
// const permissionOptions = require('../../configs/permissionOptions');

// get all documents of a collection
const roles = async (args, req) => {
  let objectObjects = [];

  try {
    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      objectObjects = await mongoRoles();
    }
    else {
      objectObjects = await redisRoles();
    }

    // return output
    return objectObjects.map(async (object) => {
      return await roleDetail(object);
    });
  } catch (error) {
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchRole = async (args, req) => {
  const FUNCTION_NAME = "searchRole";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.roleSearchInput ? args.roleSearchInput.trim().toUpperCase() : "";

  let searchObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.ROLE)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      searchObjects = await mongoSearchRole(hotelId, employeeId, searchKey);
    }
    else {
      searchObjects = await redisSearchRole(hotelId, employeeId, searchKey);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROLE_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await roleDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const getRole = async (args, req) => {
  const FUNCTION_NAME = "getRole";
  const { hotelId, employeeId } = req;
  const _id = args._id;

  let getObject = null;

  try {
    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // read single data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      getObject = await mongoGetRole(hotelId, employeeId, _id);
    }
    else {
      getObject = await redisGetRole(hotelId, employeeId, _id);
    }

    if (!getObject) throw new customError(ERR_MSG.ROLE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROLE_GET);

    // return output
    return await roleDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addRole = async (args, req) => {
  const FUNCTION_NAME = "addRole";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { name, color, permissions, description } = args.roleInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.CREATE, RESOURCE_LIST.ROLE)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (name === "") throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);

    const _name = name ? name.trim().toUpperCase() : name;
    const _color = color ? color.trim().toLowerCase() : color;
    const _description = description ? description.trim() : description;

    // add data
    const addObject = await mongoAddRole(hotelId, employeeId, _name, _color, permissions, _description);

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetRole(hotelId, employeeId, addObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROLE_ADD);

    // return output
    return await roleDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modRole = async (args, req) => {
  const FUNCTION_NAME = "modRole";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, name, color, permissions, description } = args.roleInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.EDIT, RESOURCE_LIST.ROLE)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.CONFLICT);
    if (!color) throw new customError(ERR_MSG.INVALID_COLOR, ERR_CODE.CONFLICT);

    const _name = name ? name.trim().toUpperCase() : findObject.name;
    const _color = color ? color.trim().toLowerCase() : color;
    const _description = description ? description.trim() : "";

    // modify data
    const modObject = await mongoModRole(hotelId, employeeId, _id, _name, _color, permissions, _description);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetRole(hotelId, employeeId, modObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROLE_MOD);

    // return output
    return await roleDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delRole = async (args, req) => {
  const FUNCTION_NAME = "delRole";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  let _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.REMOVE, RESOURCE_LIST.ROLE)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelRole(hotelId, employeeId, _id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisDelRole(delObject._id);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROLE_DEL);

    // return output
    return await roleDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const delRoles = async (args, req) => {
  const FUNCTION_NAME = "delRoles";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _ids } = args.rolesInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.REMOVE, RESOURCE_LIST.ROLE)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_ids) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    const delObjects = await mongoDelRoles(hotelId, employeeId, _ids[0].split(","));

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      delObjects.map(async (object) => {
        await redisDelRole(employeeId, object._id);
      })
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROLES_DEL);

    // return output
    return delObjects.map(async (object) => {
      return await roleDetail(hotelId, object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const getAllPermissionOptions = async (args, req) => {
  const FUNCTION_NAME = "getAllPermissions";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.CREATE, RESOURCE_LIST.ROLE)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    const optionsArray = require('../../configs/permissionOptions');
    const result = optionsArray.map(({ module, operations }) => ({ module, operations }));

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ROLE_GET);

    return result
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { roles, searchRole, getRole, addRole, modRole, delRole, delRoles, getAllPermissionOptions };