const fs = require('fs');
const path = require('path');

function createFolder(folderPath) {
    if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true });
    }
}

function getCurrentDate() {
    try {
        const date = new Date();
        const year = date.getFullYear();
        const month = (`0${date.getMonth() + 1}`).slice(-2); // Months are zero-based
        const day = (`0${date.getDate()}`).slice(-2);

        return `${year}-${month}-${day}`;
    } catch (error) {
        console.error(error);
    }
}

// Function to activity log messages
function writeActivityLog(hotel, employee, activity) {
    const logFolder = path.join(".", process.env.APP_LOG_FOLDER);
    const activityFolder = path.join(logFolder, process.env.APP_ACTIVITY_FOLDER);

    try {
        createFolder(logFolder);
        createFolder(activityFolder);

        const dateStr = getCurrentDate();
        const fileName = `activity-${dateStr}.log`;
        const logFilePath = path.join(activityFolder, fileName);

        const logEntry = {
            timestamp: new Date().toISOString(),
            hotel: hotel,
            employee: employee,
            activity: activity
        };

        fs.appendFile(logFilePath, JSON.stringify(logEntry) + '\n', (error) => {
            if (error) console.error('Failed to write in log file:', error);
        });
    } catch (error) {
        console.error(error);
    }
}

// Function to error log messages
function writeErrLog(hotel, employee, module, functionName, message, code = null) {
    const logFolder = path.join(".", process.env.APP_LOG_FOLDER);
    const errorFolder = path.join(logFolder, process.env.APP_ERROR_FOLDER);

    try {
        createFolder(logFolder);
        createFolder(errorFolder);

        const dateStr = getCurrentDate();
        const fileName = `error-${dateStr}.log`;
        const logFilePath = path.join(errorFolder, fileName);

        const logEntry = {
            timestamp: new Date().toISOString(),
            hotel: hotel,
            employee: employee,
            module: module,
            process: functionName,
            message: message
        };

        fs.appendFile(logFilePath, JSON.stringify(logEntry) + '\n', (error) => {
            if (error) console.error('Failed to write in log file:', error);
        });
    } catch (error) {
        console.error(error);
    }
}

// Export the log function
module.exports = { writeActivityLog, writeErrLog };