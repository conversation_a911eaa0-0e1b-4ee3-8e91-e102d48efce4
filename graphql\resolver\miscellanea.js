const { mongoMiscellanea, mongoSearchMiscellaneous, mongoGetMiscellaneous, mongoAddMiscellaneous,
  mongoModMiscellaneous, mongoDelMiscellaneous, mongoDelMiscellanea } = require("./mongo/miscellaneousMongo");
const { redisMiscellanea, redisSearchMiscellaneous, redisGetMiscellaneous, redisSetMiscellaneous,
  redisDelMiscellaneous } = require("./redis/miscellaneousRedis");
const { miscellaneousDetail } = require("../../helpers/db");
const { ACTION_LIST, RESOURCE_LIST, isAuthorized } = require("../../helpers/permissionOptions");
const { customError } = require('../../helpers/customError');
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { ACTIVITY_LIST } = require("../../helpers/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../helpers/messageOptions");
const FILE_NAME = "miscellanea.js";


// get all documents of a collection
const miscellanea = async (args, req) => {
  const FUNCTION_NAME = "miscellanea";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  let allObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.MISCELLANEOUS)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) allObjects = await mongoMiscellanea(hotelId, employeeId);
    else allObjects = await redisMiscellanea(hotelId, employeeId);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.MISCELLANEOUS_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await miscellaneousDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchMiscellaneous = async (args, req) => {
  const FUNCTION_NAME = "searchMiscellaneous";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.miscellaneousSearchInput ? args.miscellaneousSearchInput.trim().toUpperCase() : "";

  let searchObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.MISCELLANEOUS)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      searchObjects = await mongoSearchMiscellaneous(hotelId, employeeId, searchKey);
    }
    else {
      searchObjects = await redisSearchMiscellaneous(hotelId, employeeId, searchKey);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.MISCELLANEOUS_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await miscellaneousDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const getMiscellaneous = async (args, req) => {
  const FUNCTION_NAME = "getMiscellaneous";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  let getObject = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.MISCELLANEOUS)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // read single data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) getObject = await mongoGetMiscellaneous(hotelId, employeeId, _id);
    else getObject = await redisGetMiscellaneous(hotelId, employeeId, _id);
    if (!getObject) throw new customError(ERR_MSG.MISCELLANEOUS_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.MISCELLANEOUS_GET);

    // return output
    return await miscellaneousDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addMiscellaneous = async (args, req) => {
  const FUNCTION_NAME = "addMiscellaneous";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { name, unitPrice, description } = args.miscellaneousInput;

  let addObject = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.CREATE, RESOURCE_LIST.MISCELLANEOUS)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (name === "") throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);
    if (unitPrice <= 0) throw new customError(ERR_MSG.INVALID_PRICE, ERR_CODE.BAD_REQUEST);

    const _name = name.trim().toUpperCase();
    const _unitPrice = unitPrice;
    const _description = description ? description.trim() : "";

    // add data
    addObject = await mongoAddMiscellaneous(hotelId, employeeId, _name, _unitPrice, _description);

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetMiscellaneous(hotelId, employeeId, addObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.MISCELLANEOUS_ADD);

    // return output
    return await miscellaneousDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modMiscellaneous = async (args, req) => {
  const FUNCTION_NAME = "modMiscellaneous";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, name, unitPrice, description } = args.miscellaneousInput;

  let modObject = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.EDIT, RESOURCE_LIST.MISCELLANEOUS)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.CONFLICT);
    if (unitPrice <= 0) throw new customError(ERR_MSG.INVALID_PERCENTAGE, ERR_CODE.CONFLICT);

    const _name = name.trim().toUpperCase();
    const _unitPrice = unitPrice;
    const _description = description ? description.trim() : "";

    // modify data
    modObject = await mongoModMiscellaneous(hotelId, employeeId, _id, _name, _unitPrice, _description);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetMiscellaneous(hotelId, employeeId, modObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.MISCELLANEOUS_MOD);

    // return output
    return await miscellaneousDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delMiscellaneous = async (args, req) => {
  const FUNCTION_NAME = "delMiscellaneous";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.REMOVE, RESOURCE_LIST.MISCELLANEOUS)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelMiscellaneous(hotelId, employeeId, _id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) await redisDelMiscellaneous(hotelId, employeeId, delObject._id);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.MISCELLANEOUS_DEL);

    // return output
    return await miscellaneousDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const delMiscellanea = async (args, req) => {
  const FUNCTION_NAME = "delMiscellanea";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _ids } = args.miscellaneaInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.REMOVE, RESOURCE_LIST.MISCELLANEOUS)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_ids) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObjects = await mongoDelMiscellanea(hotelId, employeeId, _ids);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      delObjects.map(async (object) => {
        await redisDelMiscellaneous(hotelId, employeeId, object._id);
      });
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.MISCELLANEA_DEL);

    // return output
    return delObjects.map(async (object) => {
      return await miscellaneousDetail(object);
    });
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = {
  miscellanea, searchMiscellaneous, getMiscellaneous, addMiscellaneous,
  modMiscellaneous, delMiscellaneous, delMiscellanea
};