const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const roomCategorySchema = new Schema(
    {
        hotel: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: "Hotel"
        },
        name: {
            type: String,
            required: true,
            trim: true,
            uppercase: true
        },
        accommodation: {
            type: Number,
            required: true,
            default: 1
        },
        tariff: {
            type: Number,
            required: true
        },
        extraBedTariff: {
            type: Number,
            required: true
        },
        extraPersonTariff: {
            type: Number,
            required: true
        },
        maxDiscount: {
            type: Number,
            required: true
        },
        description: {
            type: String,
            default: function () {
                return this.description ? this.description.trim() : "";
            }
        },
        isEnable: {
            type: Boolean,
            default: true,
            required: true
        }
    },
    { timestamps: true }
);

module.exports = mongoose.model('RoomCategory', roomCategorySchema);