const mongoose = require("mongoose");
require("mongoose-double")(mongoose);
const Schema = mongoose.Schema;

const foodSchema = new Schema(
  {
    hotel: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Hotel"
    },
    name: {
      type: String,
      required: true,
      trim: true,
      uppercase: true
    },
    unitPrice: {
      type: Number,
      required: true,
      min: 0.01
    },
    description: {
      type: String,
      trim: true
    },
    isEnable: {
      type: Boolean,
      default: true,
      required: true
    },
    embedding: {
      type: [Number],
      default: []
    }
  },
  { timestamps: true }
);

module.exports = mongoose.model("Food", foodSchema);