const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { setOneRoomCategoryRedis } = require("../../../helpers/redis/roomCategory");
const { customError } = require('../../../helpers/customError');
const { writeErrLog } = require("../../../helpers/log");
const FILE_NAME = "roomCategoryRedis.js";

// get all documents of a collection
const redisRoomCategories = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "redisRoomCategories";

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ROOM_CATEGORY_FILTER}`,
      query,
      'RETURN', '9', '_id', 'hotel', 'name', 'accommodation', 'tariff', 'extraBedTariff', 'extraPersonTariff', 'maxDiscount', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '30'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results;
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const redisSearchRoomCategory = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "redisSearchRoomCategory";
  const regex = new RegExp(searchKey, "i");

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ROOM_CATEGORY_FILTER}`,
      query,
      'RETURN', '9', '_id', 'hotel', 'name', 'accommodation', 'tariff', 'extraBedTariff', 'extraPersonTariff', 'maxDiscount', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '30'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      if (!searchKey.trim()) {
        return results;
      }
      else {
        // filter array
        const filter = results.filter((item) => regex.test(item.name) ||
          regex.test(item.accommodation) ||
          regex.test(item.tariff) ||
          regex.test(item.extraBedTariff) ||
          regex.test(item.extraPersonTariff) ||
          regex.test(item.description));

        return filter;
      }
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const redisGetRoomCategory = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisGetRoomCategory";

  try {
    // read single data
    const query = `(@hotel:{${hotelId}} @isEnable:{true} @_id:{${id}})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ROOM_CATEGORY_UNIQUE}`,
      query,
      'RETURN', '9', '_id', 'hotel', 'name', 'accommodation', 'tariff', 'extraBedTariff', 'extraPersonTariff', 'maxDiscount', 'description',
      'LIMIT', '0', '1'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results[0];
    }

    return {};
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisSetRoomCategory = async (hotelId, employeeId, data) => {
  const FUNCTION_NAME = "redisSetRoomCategory";

  try {
    // read single data
    await setOneRoomCategoryRedis(hotelId, data);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisDelRoomCategory = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisDelRoomCategory";

  try {
    // delete single data
    const result = await redis.del(`${process.env.HASH_ROOM_CATEGORY}_${hotelId}:${id}`);
    return result;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = {
  redisRoomCategories, redisSearchRoomCategory, redisGetRoomCategory,
  redisSetRoomCategory, redisDelRoomCategory
};