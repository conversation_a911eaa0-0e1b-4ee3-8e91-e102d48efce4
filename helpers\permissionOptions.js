const { ROLE_LIST, getRoleName } = require("./roleOptions");

const ACTION_LIST = {
    VIEW: "VIEW",
    CREATE: "CREATE",
    EDIT: "EDIT",
    REMOVE: "REMOVE"
}

const RESOURCE_LIST = {
    HOTEL: "HOTEL",
    ROL<PERSON>: "ROLE",
    GST: "GST",
    EMPLOYEE: "EMPLOYEE",
    AGENT: "AGENT",
    ID: "ID",
    PAYMENT_MODE: "PAYMENT_MODE",
    PLAN: "PLAN",
    ROOM_CATEGORY: "ROOM_CATEGORY",
    FOOD: "FOOD",
    SERVICE: "SERVICE",
    MISCELLANEOUS: "MISCELLANEOUS",
    ROOM: "ROOM",
    TABLE: "TABLE",
    GUEST: "GUEST",
    BOOKING: "BOOKING",
    BREAKFAST: "BREAKFAST",
    ORDER: "ORDER",
    BILL: "BILL",
    PAYMENT: "PAYMENT",
    LEDGER: "LEDGER",
    YEAR_END: "YEAR_END",
    ACTIVITY_LOG: "ACTIVITY_LOG",
    ERROR_LOG: "ERROR_LOG"
}

const PERMISSION_LIST = {
    VIEW_HOTEL: [ROLE_LIST.SYSTEM_ADMIN],
    CREATE_HOTEL: [ROLE_LIST.SYSTEM_ADMIN],
    EDIT_HOTEL: [ROLE_LIST.SYSTEM_ADMIN],
    REMOVE_HOTEL: [ROLE_LIST.SYSTEM_ADMIN],

    VIEW_ROLE: [ROLE_LIST.SYSTEM_ADMIN],
    CREATE_ROLE: [ROLE_LIST.SYSTEM_ADMIN],
    EDIT_ROLE: [ROLE_LIST.SYSTEM_ADMIN],
    REMOVE_ROLE: [ROLE_LIST.SYSTEM_ADMIN],

    VIEW_GST: [
        ROLE_LIST.SYSTEM_ADMIN,
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN
    ],
    CREATE_GST: [ROLE_LIST.SYSTEM_ADMIN],
    EDIT_GST: [ROLE_LIST.SYSTEM_ADMIN],
    REMOVE_GST: [ROLE_LIST.SYSTEM_ADMIN],

    VIEW_EMPLOYEE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_EMPLOYEE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN
    ],
    EDIT_EMPLOYEE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.HOTEL_ACCOUNTANT,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    REMOVE_EMPLOYEE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN
    ],

    VIEW_AGENT: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_AGENT: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN],
    EDIT_AGENT: [ROLE_LIST.HOTEL_ADMIN],
    REMOVE_AGENT: [ROLE_LIST.HOTEL_ADMIN],

    VIEW_ID: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_ID: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN],
    EDIT_ID: [ROLE_LIST.HOTEL_ADMIN],
    REMOVE_ID: [ROLE_LIST.HOTEL_ADMIN],

    VIEW_PAYMENT_MODE: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_PAYMENT_MODE: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN],
    EDIT_PAYMENT_MODE: [ROLE_LIST.HOTEL_ADMIN],
    REMOVE_PAYMENT_MODE: [ROLE_LIST.HOTEL_ADMIN],

    VIEW_PLAN: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_PLAN: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN],
    EDIT_PLAN: [ROLE_LIST.HOTEL_ADMIN],
    REMOVE_PLAN: [ROLE_LIST.HOTEL_ADMIN],

    VIEW_ROOM_CATEGORY: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_ROOM_CATEGORY: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN],
    EDIT_ROOM_CATEGORY: [ROLE_LIST.HOTEL_ADMIN],
    REMOVE_ROOM_CATEGORY: [ROLE_LIST.HOTEL_ADMIN],

    VIEW_FOOD: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF],
    CREATE_FOOD: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.KITCHEN_ADMIN],
    EDIT_FOOD: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.KITCHEN_ADMIN],
    REMOVE_FOOD: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.KITCHEN_ADMIN],

    VIEW_SERVICE: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_SERVICE: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.HOUSEKEEPING_ADMIN],
    EDIT_SERVICE: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.HOUSEKEEPING_ADMIN],
    REMOVE_SERVICE: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.HOUSEKEEPING_ADMIN],

    VIEW_MISCELLANEOUS: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_MISCELLANEOUS: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.HOUSEKEEPING_ADMIN],
    EDIT_MISCELLANEOUS: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.HOUSEKEEPING_ADMIN],
    REMOVE_MISCELLANEOUS: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.HOUSEKEEPING_ADMIN],

    VIEW_ROOM: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_ROOM: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN],
    EDIT_ROOM: [ROLE_LIST.HOTEL_ADMIN],
    REMOVE_ROOM: [ROLE_LIST.HOTEL_ADMIN],

    VIEW_TABLE: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_TABLE: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF],
    EDIT_TABLE: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF],
    REMOVE_TABLE: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF],

    VIEW_GUEST: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_GUEST: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF],
    EDIT_GUEST: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF],
    REMOVE_GUEST: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF],

    VIEW_BOOKING: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_BOOKING: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF],
    EDIT_BOOKING: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF],
    REMOVE_BOOKING: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF],

    VIEW_BREAKFAST: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_BREAKFAST: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],
    EDIT_BREAKFAST: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],
    REMOVE_BREAKFAST: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],

    VIEW_ORDER: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_ORDER: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],
    EDIT_ORDER: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],
    REMOVE_ORDER: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],

    VIEW_BILL: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_BILL: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],
    EDIT_BILL: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],
    REMOVE_BILL: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],

    VIEW_PAYMENT: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_PAYMENT: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],
    EDIT_PAYMENT: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],
    REMOVE_PAYMENT: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],

    VIEW_LEDGER: [ROLE_LIST.SYSTEM_ADMIN, ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],
    VIEW_BILL: [ROLE_LIST.SYSTEM_ADMIN, ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],

    VIEW_ACTIVITY_LOG: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_ACTIVITY_LOG: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],
    EDIT_ACTIVITY_LOG: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],
    REMOVE_ACTIVITY_LOG: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],

    VIEW_ERROR_LOG: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.VIEW_ONLY, ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_ERROR_LOG: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],
    EDIT_ERROR_LOG: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF],
    REMOVE_ERROR_LOG: [ROLE_LIST.HOTEL_ADMIN, ROLE_LIST.RECEPTION_ADMIN, ROLE_LIST.RECEPTION_STAFF, ROLE_LIST.RESTAURANT_ADMIN, ROLE_LIST.RESTAURANT_STAFF, ROLE_LIST.KITCHEN_ADMIN, ROLE_LIST.KITCHEN_STAFF, ROLE_LIST.HOUSEKEEPING_ADMIN, ROLE_LIST.HOUSEKEEPING_STAFF]
};

const isAuthorized = async (userRole, action, resource) => {
    const permission = action + "_" + resource;
    const rolls = PERMISSION_LIST[permission];
    const results = rolls.map(role => role === userRole ? true : false);

    return results.includes(true);
};

module.exports = { ACTION_LIST, RESOURCE_LIST, isAuthorized };