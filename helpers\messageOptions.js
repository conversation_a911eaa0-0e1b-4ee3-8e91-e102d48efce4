const SUCCESS_CODE = {
  OK: 200,
  CREATED: 201
};

const SUCCESS_MSG = {
  OK: "OK",
  OTP: "OTP successfully sent",
  PASSWORD_CHANGER: "Password changed successfully",
  OPERATION: "Operation successfully done"
};

const ERR_CODE = {
  BAD_REQUEST: 400,
  UNAUTHORIZE: 401,
  FORBIDDEN: 403,
  NOT_EXISTS: 404,
  NOT_ALLOWED: 405,
  CONFLICT: 409,
  INTERNAL: 500
};

const ERR_MSG = {
  INVALID_REQUEST: "Invalid request!",
  INVALID_ID: "Invalid id!",
  INVALID_HOTEL: "Invalid hotel id!",
  INVALID_OLD_PASSWORD: "Invalid old password!",
  INVALID_PASSWORD: "Invalid password!",
  INVALID_OTP: "Invalid OTP!",
  INVALID_EMPLOYEE: "Invalid employee!",
  INVALID_ROLE: "Invalid role!",
  INVALID_COLOR: "Invalid color!",
  INVALID_CATEGORY: "Invalid category!",
  INVALID_PLAN: "Invalid plan!",
  INVALID_NAME: "Invalid name!",
  INVALID_ADDRESS: "Invalid address!",
  INVALID_CITY: "Invalid city!",
  INVALID_PIN: "Invalid pin!",
  INVALID_STATE: "Invalid state!",
  INVALID_PHONE: "Invalid phone number!",
  INVALID_EMAIL: "Invalid email address!",
  INVALID_MOBILE: "Invalid mobile number!",
  INVALID_PS: "Invalid police station!",
  INVALID_FATHER: "Invalid father name!",
  INVALID_AGE: "Invalid age!",
  INVALID_GUEST_COUNT: "Invalid guest count!",
  INVALID_MALE_GUEST_COUNT: "Invalid male guest count!",
  INVALID_FEMALE_GUEST_COUNT: "Invalid female guest count!",
  INVALID_AGENT: "Invalid agent!",
  INVALID_GST: "Invalid GST number!",
  INVALID_MAX_TARIFF: "Invalid Max. tariff!",
  INVALID_MAI_TARIFF: "Invalid Mix. tariff!",
  INVALID_PERCENTAGE: "Invalid percentage!",
  INVALID_PRICE: "Invalid price!",
  INVALID_NO: "Invalid number!",
  INVALID_ACCOMMODATION: "Invalid accommodation count!",
  INVALID_ROOM: "Invalid room!",
  INVALID_TARIFF: "Invalid tariff!",
  INVALID_DISCOUNT: "Invalid discount!",
  INVALID_EXTRA_BED_TARIFF: "Invalid extra bed tariff!",
  INVALID_EXTRA_PERSON_TARIFF: "Invalid extra person tariff!",
  INVALID_TABLE: "Invalid table!",
  INVALID_ROOM_OR_TABLE: "Invalid room or table!",
  INVALID_FOOD: "Invalid food!",
  INVALID_DATE: "Invalid date!",
  INVALID_STATUS: "Invalid status!",
  INVALID_PAYMENT_MODE: "Invalid payment mode!",
  INVALID_AMOUNT: "Invalid amount!",
  INVALID_SERVICE: "Invalid service!",
  INVALID_TYPE: "Invalid type!",
  INVALID_GUEST: "Invalid guest!",
  INVALID_BILL: "Invalid bill!",
  INVALID_BOOKING: "Invalid booking!",
  INVALID_TOKEN_ID: "Invalid token id!",
  INVALID_CHECKOUT: "Invalid checkout!",
  INVALID_ACTIVITY: "Invalid activity!",

  UNAUTHORIZE: "Unauthorized user!",
  FORBIDDEN: "Forbidden user!",
  NO_HOTEL: "No hotel selected!",
  OTP: "OTP send error!",
  OTP_EXPIRE: "OTP expire!",
  OTP_REQUIRE: "OTP required!",
  EMPLOYEE_NAME_REQUIRE: "Mobile no. or email required!",
  EXISTS: "Item already exists!",
  IN_USE: "Item already in use in other objects!",

  HOTEL_CONFLICT: "Hotel already exists!",
  EMPLOYEE_CONFLICT: "Employee already exists!",
  AGENT_CONFLICT: "Agent already exists!",
  GST_CONFLICT: "GST already exists!",
  IDENTIFICATION_CONFLICT: "Identification already exists!",
  ROLE_CONFLICT: "Role already exists!",
  CATEGORY_CONFLICT: "Category already exists!",
  PLAN_CONFLICT: "Plan already exists!",
  PAYMENT_MODE_CONFLICT: "Payment mode already exists!",
  FOOD_CONFLICT: "Food already exists!",
  SERVICE_CONFLICT: "Service already exists!",
  MISCELLANEOUS_CONFLICT: "Miscellaneous already exists!",
  ROOM_CONFLICT: "Room already exists!",
  TABLE_CONFLICT: "Table already exists!",
  GUEST_CONFLICT: "Guest already exists!",
  BOOKING_CONFLICT: "Booking already exists!",
  ORDER_CONFLICT: "Order already exists!",
  BILL_CONFLICT: "Bill already exists!",
  PAYMENT_CONFLICT: "Payment already exists!",
  ACTIVITY_CONFLICT: "activity already exists!",

  HOTEL_NOT_EXISTS: "Hotel not found!",
  EMPLOYEE_NOT_EXISTS: "Employee not found!",
  AGENT_NOT_EXISTS: "Agent not found!",
  GST_NOT_EXISTS: "GST not found!",
  IDENTIFICATION_NOT_EXISTS: "Identification not found!",
  ROLE_NOT_EXISTS: "Role not found!",
  CATEGORY_NOT_EXISTS: "Category not found!",
  PLAN_NOT_EXISTS: "Plan not found!",
  PAYMENT_MODE_NOT_EXISTS: "Payment mode not found!",
  TOKEN_ID_NOT_EXISTS: "Token not exists!",
  ORDER_STATUS_NOT_EXISTS: "Order status not exists",
  DELIVERY_DATE_NOT_EXISTS: "Delivery date not exist!",
  DELIVERY_TIME_NOT_EXISTS: "Delivery time not exist!",
  FOOD_NOT_EXISTS: "Food not found!",
  SERVICE_NOT_EXISTS: "Service not found!",
  MISCELLANEOUS_NOT_EXISTS: "Miscellaneous not found!",
  ROOM_NOT_EXISTS: "Room not found!",
  TABLE_NOT_EXISTS: "Table not found!",
  GUEST_NOT_EXISTS: "Guest not found!",
  BOOKING_NOT_EXISTS: "Booking not found!",
  ORDER_NOT_EXISTS: "Order not found!",
  BILL_NOT_EXISTS: "Bill not found!",
  PAYMENT_NOT_EXISTS: "Payment not found!",
  ACTIVITY_NOT_EXISTS: "Activity not found!",
  ERROR_NOT_EXISTS: "Error not found!",

  HOTEL_NOT_SAVE: "Hotel not saved!",
  EMPLOYEE_NOT_SAVE: "Employee not saved!",
  PASSWORD_NOT_SAVE: "Password not saved!",
  AGENT_NOT_SAVE: "Agent not saved!",
  GST_NOT_SAVE: "GST not saved!",
  IDENTIFICATION_NOT_SAVE: "Identification not saved!",
  ROLE_NOT_SAVE: "Role not saved!",
  CATEGORY_NOT_SAVE: "Category not saved!",
  PLAN_NOT_SAVE: "Plan not saved!",
  PAYMENT_MODE_NOT_SAVE: "Payment mode not saved!",
  FOOD_NOT_SAVE: "Food not saved!",
  SERVICE_NOT_SAVE: "Service not saved!",
  MISCELLANEOUS_NOT_SAVE: "Miscellaneous not saved!",
  ROOM_NOT_SAVE: "Room not saved!",
  TABLE_NOT_SAVE: "Table not saved!",
  GUEST_NOT_SAVE: "Guest not saved!",
  BOOKING_NOT_SAVE: "Booking not saved!",
  ROOM_NOT_AVAILABLE: "Room not available!",
  ORDER_NOT_SAVE: "Order not saved!",
  BILL_NOT_SAVE: "Bill not saved!",
  PAYMENT_NOT_SAVE: "Payment not saved!",
  ACTIVITY_NOT_SAVE: "Activity not saved!",
  ERROR_NOT_SAVE: "Error not saved!",

  HOTEL_NOT_DELETE: "Hotel not deleted!",
  EMPLOYEE_NOT_DELETE: "Employee not deleted!",
  AGENT_NOT_DELETE: "Agent not deleted!",
  GST_NOT_DELETE: "GST not deleted!",
  IDENTIFICATION_NOT_DELETE: "Identification not deleted!",
  ROLE_NOT_DELETE: "Role not deleted!",
  CATEGORY_NOT_DELETE: "Category not deleted!",
  PLAN_NOT_DELETE: "Plan not deleted!",
  PAYMENT_MODE_NOT_DELETE: "Payment mode not deleted!",
  FOOD_NOT_DELETE: "Food not deleted!",
  SERVICE_NOT_DELETE: "Service not deleted!",
  MISCELLANEOUS_NOT_DELETE: "Miscellaneous not deleted!",
  ROOM_NOT_DELETE: "Room not deleted!",
  TABLE_NOT_DELETE: "Table not deleted!",
  GUEST_NOT_DELETE: "Guest not deleted!",
  BOOKING_NOT_DELETE: "Booking not deleted!",
  ORDER_NOT_DELETE: "Order not deleted!",
  BILL_NOT_DELETE: "Bill not deleted!",
  PAYMENT_NOT_DELETE: "Payment not deleted!",
  BILL_NOT_ATTACHED: "Bill not attached!",
  ACTIVITY_NOT_DELETE: "Activity not deleted!",
  ERROR_NOT_DELETE: "Error not deleted!",

  API: "Internal error!"
};

module.exports = { SUCCESS_CODE, SUCCESS_MSG, ERR_CODE, ERR_MSG };
