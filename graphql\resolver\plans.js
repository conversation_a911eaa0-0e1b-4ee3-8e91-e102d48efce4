const { mongoPlans, mongoSearchPlan, mongoGetPlan, mongoAddPlan, mongoModPlan, mongoDelPlan, mongoDelPlans } = require("./mongo/planMongo");
const { redisPlans, redisSearchPlan, redisGetPlan, redisSetPlan, redisDelPlan } = require("./redis/planRedis");
const { planDetail } = require("../../helpers/db");
const { ACTION_LIST, RESOURCE_LIST, isAuthorized } = require("../../helpers/permissionOptions");
const { customError } = require('../../helpers/customError');
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { ACTIVITY_LIST } = require("../../helpers/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../helpers/messageOptions");
const FILE_NAME = "plans.js";

// get all documents of a collection
const plans = async (args, req) => {
  const FUNCTION_NAME = "plans";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  let allObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.PLAN)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      allObjects = await mongoPlans(hotelId, employeeId);
    }
    else {
      allObjects = await redisPlans(hotelId, employeeId);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PLAN_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await planDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchPlan = async (args, req) => {
  const FUNCTION_NAME = "searchPlan";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.planSearchInput ? args.planSearchInput.trim().toUpperCase() : "";

  let searchObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.PLAN)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      searchObjects = await mongoSearchPlan(hotelId, employeeId, searchKey);
    }
    else {
      searchObjects = await redisSearchPlan(hotelId, employeeId, searchKey);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PLAN_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await planDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const getPlan = async (args, req) => {
  const FUNCTION_NAME = "getPlan";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  let getObject = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.PLAN)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // read single data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      getObject = await mongoGetPlan(hotelId, employeeId, _id);
    }
    else {
      getObject = await redisGetPlan(hotelId, employeeId, _id);
    }

    if (!getObject) throw new customError(ERR_MSG.PLAN_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PLAN_GET);

    // return output
    return await planDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addPlan = async (args, req) => {
  const FUNCTION_NAME = "addPlan";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { name, description } = args.planInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.CREATE, RESOURCE_LIST.PLAN)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (name === "") throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);

    const _name = name.trim().toUpperCase();
    const _description = description ? description.trim() : "";

    // add data
    const addObject = await mongoAddPlan(hotelId, employeeId, _name, _description);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetPlan(hotelId, employeeId, addObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PLAN_ADD);

    // return output
    return await planDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modPlan = async (args, req) => {
  const FUNCTION_NAME = "modPlan";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, name, description } = args.planInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.EDIT, RESOURCE_LIST.PLAN)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.CONFLICT);

    const _name = name.trim().toUpperCase();
    const _description = description ? description.trim() : "";

    // modify data
    const modObject = await mongoModPlan(hotelId, employeeId, _id, _name, _description);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetPlan(hotelId, employeeId, modObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PLAN_MOD);

    // return output
    return await planDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delPlan = async (args, req) => {
  const FUNCTION_NAME = "delPlan";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.REMOVE, RESOURCE_LIST.PLAN)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelPlan(hotelId, employeeId, _id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisDelPlan(hotelId, employeeId, delObject._id);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PLAN_DEL);

    // return output
    return await planDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const delPlans = async (args, req) => {
  const FUNCTION_NAME = "delAgents";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _ids } = args.plansInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.REMOVE, RESOURCE_LIST.PLAN)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_ids) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObjects = await mongoDelPlans(hotelId, employeeId, _ids[0].split(","));

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      delObjects.map(async (object) => {
        await redisDelPlan(hotelId, employeeId, object._id);
      })
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.AGENTS_DEL);

    // return output
    return delObjects.map(async (object) => {
      return await planDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { plans, searchPlan, getPlan, addPlan, modPlan, delPlan, delPlans };