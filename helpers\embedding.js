const fetch = require("node-fetch");
const { OpenAIEmbeddings } = require("@langchain/openai");
const { customError } = require('./customError');

const embeddings = new OpenAIEmbeddings({
    apiKey: process.env.OPENAI_API_KEY,
    batchSize: 512,
    model: process.env.OPENAI_EMBEDDING_MODEL
});

async function createEmbedding(text) {
    try {
        const result = await embeddings.embedQuery(text);
        return result;
    } catch (error) {
        throw new customError(error.message, error.code);
    }
}



// Replace with your Hugging Face API token
const HF_API_TOKEN = "*************************************";

// Step 1: Convert JSON to readable text
function jsonToText(json) {
    return Object.entries(json)
        .map(([key, value]) => `${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`)
        .join(". ");
}

// Step 2: Summarize text using BART
async function summarizeText(text) {
    const response = await fetch("https://api-inference.huggingface.co/models/facebook/bart-large-cnn", {
        method: "POST",
        headers: {
            Authorization: `Bearer ${HF_API_TOKEN}`,
            "Content-Type": "application/json"
        },
        body: JSON.stringify({ inputs: text })
    });

    const result = await response.json();
    if (Array.isArray(result) && result[0]?.summary_text) {
        return result[0].summary_text;
    } else {
        throw new Error("Summarization failed: " + JSON.stringify(result));
    }
}

// Step 3: Generate 1536-dim embedding using GTE-Large
async function generateEmbedding(text) {
    const response = await fetch("https://api-inference.huggingface.co/pipeline/feature-extraction/thenlper/gte-large", {
        method: "POST",
        headers: {
            Authorization: `Bearer ${HF_API_TOKEN}`,
            "Content-Type": "application/json"
        },
        body: JSON.stringify({ inputs: text })
    });

    const result = await response.json();
    if (Array.isArray(result) && result.length === 1536) {
        return result;
    } else {
        throw new Error("Embedding failed: " + JSON.stringify(result));
    }
}

// 🔗 Chain the steps
async function processJsonToEmbedding(json) {
    try {
        const text = jsonToText(json);
        console.log("📝 Generated Text:", text);

        const summary = await summarizeText(text);
        console.log("📄 Summary:", summary);

        const embedding = await generateEmbedding(summary);
        console.log("🔢 Embedding (first 5 values):", embedding.slice(0, 5));
        return embedding;
    } catch (err) {
        console.error("❌ Error:", err.message);
    }
}

// 🔗 Chain the steps
async function processTextToEmbedding(text) {
    try {
        const summary = await summarizeText(text);
        console.log("📄 Summary:", summary);

        const embedding = await generateEmbedding(summary);
        console.log("🔢 Embedding (first 5 values):", embedding.slice(0, 5));
        return embedding;
    } catch (err) {
        console.error("❌ Error:", err.message);
    }
}

module.exports = {
    createEmbedding,
    processJsonToEmbedding,
    processTextToEmbedding
};