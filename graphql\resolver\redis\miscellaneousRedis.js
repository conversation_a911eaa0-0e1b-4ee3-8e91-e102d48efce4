const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { setOneMiscellaneousRedis } = require("../../../helpers/redis/miscellaneous");
const { customError } = require('../../../helpers/customError');
const { writeErrLog } = require("../../../helpers/log");
const FILE_NAME = "miscellaneousRedis.js";

// get all documents of a collection
const redisMiscellanea = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "redisMiscellanea";

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_MISCELLANEOUS_FILTER}`,
      query,
      'RETURN', '5', '_id', 'hotel', 'name', 'unitPrice', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '1000'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results;
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const redisSearchMiscellaneous = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "redisSearchMiscellaneous";
  const regex = new RegExp(searchKey, "i");

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_MISCELLANEOUS_FILTER}`,
      query,
      'RETURN', '5', '_id', 'hotel', 'name', 'unitPrice', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '1000'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      if (!searchKey.trim()) {
        return results;
      }
      else {
        // filter array
        const filter = results.filter((item) => regex.test(item.name) ||
          regex.test(item.unitPrice)) ||
          regex.test(item.description);

        return filter;
      }
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const redisGetMiscellaneous = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisGetMiscellaneous";

  try {
    // read single data
    const query = `(@hotel:{${hotelId}} @isEnable:{true} @_id:{${id}})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_MISCELLANEOUS_UNIQUE}`,
      query,
      'RETURN', '5', '_id', 'hotel', 'name', 'unitPrice', 'description',
      'LIMIT', '0', '1'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results[0];
    }

    return {};
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisSetMiscellaneous = async (hotelId, employeeId, data) => {
  const FUNCTION_NAME = "redisSetMiscellaneous";

  try {
    // read single data
    await setOneMiscellaneousRedis(hotelId, data);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisDelMiscellaneous = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisDelMiscellaneous";

  try {
    // delete single data
    const result = await redis.del(`${process.env.HASH_MISCELLANEOUS}_${hotelId}:${id}`);
    return result;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = {
  redisMiscellanea, redisSearchMiscellaneous, redisGetMiscellaneous,
  redisSetMiscellaneous, redisDelMiscellaneous
};