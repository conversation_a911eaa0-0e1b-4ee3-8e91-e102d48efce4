const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { processTextToEmbedding } = require("../../../helpers/embedding");
const { customError } = require('../../../helpers/customError');
const { writeErrLog } = require("../../../helpers/log");
const FILE_NAME = "foodRedisAI.js";

// search with ai (vector) within collection
const redisAISearchFood = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "redisAISearchFood";

  try {
    if (searchKey === "") {
      // get all data
      const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
      const command = new Command('FT.SEARCH', [
        `${process.env.INDEX_FOOD_FILTER}`,
        query,
        'RETURN', '5', '_id', 'hotel', 'name', 'unitPrice', 'description'
      ]);
      const data = await redis.sendCommand(command);

      // Process the results
      if (data && data.length > 1) {
        const results = decodeRedisVectorResults(data);
        return results;
      }
    }
    else {
      // const searchEmbedding = await createEmbedding(searchKey.toUpperCase());
      const searchEmbedding = await processTextToEmbedding(searchKey.toUpperCase());
      const vector = Float32Array.from(searchEmbedding);
      const buffer = Buffer.from(vector.buffer);
      const query = `(@hotel:{${hotelId}} @isEnable:{true})=>[KNN 10 @embedding $param AS score]`;
      const command = new Command('FT.SEARCH', [
        `${process.env.DB_FOOD_INDEX_VECTOR}`,
        query,
        'PARAMS', '2',
        'param', buffer,
        'RETURN', '6', '_id', 'hotel', 'name', 'unitPrice', 'description', 'score',
        'SORTBY', 'score',
        'DIALECT', '2'
      ]);
      const data = await redis.sendCommand(command);

      // Process the results
      if (data && data.length > 1) {
        const results = decodeRedisVectorResults(data);
        return results.filter(result => parseFloat(result.score) <= 0.7);
      }
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { redisAISearchFood };