const { mongoGSTs, mongoSearchGST, mongoGetGST, mongoAddGST, mongoModGST, mongoDelGST } = require("./mongo/gstMongo");
const { redisGSTs, redisSearchGST, redisSetGST, redisDelGST } = require("./redis/gstRedis");
const { getOneGSTRedis, } = require("../../helpers/redis/gst");
const { gstDetail } = require("../../helpers/db");
const { ACTION_LIST, RESOURCE_LIST, isAuthorized } = require("../../helpers/permissionOptions");
const { customError } = require('../../helpers/customError');
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { ACTIVITY_LIST } = require("../../helpers/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../helpers/messageOptions");
const FILE_NAME = "gsts.js";

// get all documents of a collection
const gsts = async (args, req) => {
  const FUNCTION_NAME = "gsts";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  let allObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.GST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      allObjects = await mongoGSTs(hotelId, employeeId);
    }
    else {
      allObjects = await redisGSTs(hotelId, employeeId);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.GST_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await gstDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchGST = async (args, req) => {
  const FUNCTION_NAME = "searchGST";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchTariff = args.gstSearchInput ? args.gstSearchInput.trim() : "";

  let searchObjects = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.GST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // search data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      searchObjects = await mongoSearchGST(hotelId, employeeId, searchTariff);
    }
    else {
      searchObjects = await redisSearchGST(hotelId, employeeId, searchTariff);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.GST_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await gstDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const getGST = async (args, req) => {
  const FUNCTION_NAME = "getGST";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  let getObject = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.GST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // read single data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      getObject = await mongoGetGST(hotelId, employeeId, _id);
    }
    else {
      getObject = await getOneGSTRedis(_id);
    }

    if (!getObject) throw new customError(ERR_MSG.GST_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.GST_GET);

    // return output
    return await gstDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addGST = async (args, req) => {
  const FUNCTION_NAME = "addGST";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { minTariff, maxTariff, iGSTPercentage, sGSTPercentage } = args.gstInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.CREATE, RESOURCE_LIST.GST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (minTariff < 0) throw new customError(ERR_MSG.INVALID_MAI_TARIFF, ERR_CODE.BAD_REQUEST);
    if (maxTariff <= minTariff) throw new customError(ERR_MSG.INVALID_MAX_TARIFF, ERR_CODE.BAD_REQUEST);
    if (iGSTPercentage < 0) throw new customError(ERR_MSG.INVALID_PERCENTAGE, ERR_CODE.BAD_REQUEST);
    if (sGSTPercentage < 0) throw new customError(ERR_MSG.INVALID_PERCENTAGE, ERR_CODE.BAD_REQUEST);

    // add data
    const addObject = await mongoAddGST(hotelId,
      employeeId, minTariff, maxTariff,
      iGSTPercentage, sGSTPercentage);

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetGST(hotelId, employeeId, addObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.GST_ADD);

    // return output
    return await gstDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modGST = async (args, req) => {
  const FUNCTION_NAME = "modGST";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, maxTariff, minTariff, iGSTPercentage, sGSTPercentage } = args.gstInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.EDIT, RESOURCE_LIST.GST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (minTariff < 0) throw new customError(ERR_MSG.INVALID_MAI_TARIFF, ERR_CODE.BAD_REQUEST);
    if (maxTariff <= minTariff) throw new customError(ERR_MSG.INVALID_MAX_TARIFF, ERR_CODE.BAD_REQUEST);
    if (iGSTPercentage < 0) throw new customError(ERR_MSG.INVALID_PERCENTAGE, ERR_CODE.BAD_REQUEST);
    if (sGSTPercentage < 0) throw new customError(ERR_MSG.INVALID_PERCENTAGE, ERR_CODE.BAD_REQUEST);

    // modify data
    const modObject = await mongoModGST(hotelId, employeeId, _id,
      minTariff, maxTariff, iGSTPercentage, sGSTPercentage);

    // redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetGST(hotelId, employeeId, modObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.GST_MOD);

    return await gstDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delGST = async (args, req) => {
  const FUNCTION_NAME = "delGST";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.REMOVE, RESOURCE_LIST.GST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelGST(hotelId, employeeId, _id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisDelGST(hotelId, employeeId, delObject._id);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.GST_DEL);

    // return output
    return await gstDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { gsts, searchGST, getGST, addGST, modGST, delGST };