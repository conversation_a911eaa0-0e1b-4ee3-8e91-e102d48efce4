const fs = require('fs');
const path = require('path');
const AdmZip = require('adm-zip');
const { MongoClient } = require('mongodb');
const { ObjectId } = require('mongodb');
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { ACTIVITY_LIST } = require("../../helpers/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../helpers/messageOptions");

const { mongoAgents } = require('./mongo/agentMongo');
const { mongoFoods } = require('./mongo/foodMongo');
const { mongoMiscellanea } = require('./mongo/miscellaneousMongo');
const { mongoTables } = require('./mongo/tableMongo');

const { mongoAIModAgent } = require('./mongo/agentMongoAI');
const { mongoAIModFood } = require('./mongo/foodMongoAI');
const { mongoAIModMiscellaneous } = require('./mongo/miscellaneousMongoAI');
const { mongoAIModTable } = require('./mongo/tableMongoAI');

const FILE_NAME = "dbOperations.js";

const preprocessData = (data) => {
  return data.map((entry) => ({
    ...entry,
    _id: new ObjectId(entry._id),
    createdAt: new Date(entry.createdAt),
    updatedAt: new Date(entry.updatedAt),
  }));
};

const preprocessWithHotelData = (data) => {
  return data.map((entry) => ({
    ...entry,
    _id: new ObjectId(entry._id),
    hotel: new ObjectId(entry.hotel),
    createdAt: new Date(entry.createdAt),
    updatedAt: new Date(entry.updatedAt),
  }));
};

const preprocessRoomData = (data) => {
  return data.map((entry) => ({
    ...entry,
    _id: new ObjectId(entry._id),
    hotel: new ObjectId(entry.hotel),
    category: new ObjectId(entry.category),
    createdAt: new Date(entry.createdAt),
    updatedAt: new Date(entry.updatedAt),
  }));
};

const preprocessGuestData = (data) => {
  return data.map((entry) => {
    if (entry.identification) {
      return {
        ...entry,
        _id: new ObjectId(entry._id),
        hotel: new ObjectId(entry.hotel),
        identification: new ObjectId(entry.identification),
        employee: new ObjectId(entry.employee),
        createdAt: new Date(entry.createdAt),
        updatedAt: new Date(entry.updatedAt)
      }
    } else {
      return {
        ...entry,
        _id: new ObjectId(entry._id),
        hotel: new ObjectId(entry.hotel),
        employee: new ObjectId(entry.employee),
        createdAt: new Date(entry.createdAt),
        updatedAt: new Date(entry.updatedAt)
      }
    }
  });
};

const preprocessBookingData = (data) => {
  return data.map((entry) => ({
    ...entry,
    _id: new ObjectId(entry._id),
    hotel: new ObjectId(entry.hotel),
    plan: new ObjectId(entry.plan),
    agent: new ObjectId(entry.agent),
    guest: new ObjectId(entry.guest),
    rooms: preprocessRoomArray(entry.rooms),
    startDate: new Date(entry.startDate),
    endDate: new Date(entry.endDate),
    createdAt: new Date(entry.createdAt),
    updatedAt: new Date(entry.updatedAt)
  }));
};

const preprocessRoomArray = (data) => {
  return data.map((entry) => ({
    ...entry,
    _id: new ObjectId(entry._id),
    room: new ObjectId(entry.room),
    occupancyDate: new Date(entry.occupancyDate)
  }));
};

const preprocessOrderData = (data) => {
  return data.map((entry) => {
    if (entry.booking !== null) {
      return {
        ...entry,
        _id: new ObjectId(entry._id),
        hotel: new ObjectId(entry.hotel),
        booking: new ObjectId(entry.booking),
        tokens: processTokensArray(entry.tokens),
        createdAt: new Date(entry.createdAt),
        updatedAt: new Date(entry.updatedAt)
      }
    } else {
      return {
        ...entry,
        _id: new ObjectId(entry._id),
        hotel: new ObjectId(entry.hotel),
        tokens: processTokensArray(entry.tokens),
        createdAt: new Date(entry.createdAt),
        updatedAt: new Date(entry.updatedAt)
      }
    };
  })
};

const processTokensArray = (dataArray) => {
  return dataArray.map((token) => {
    return {
      _id: new ObjectId(token._id),
      tokenNo: token.tokenNo,
      status: token.status,
      items: token.items.map((item) => ({
        ...item,
        _id: new ObjectId(item._id),
        item: new ObjectId(item.item)
      })),
      createdAt: new Date(token.createdAt),
      updatedAt: new Date(token.updatedAt)
    };
  });
};

const preprocessBillData = (data) => {
  return data.map((entry) => {
    if (entry.booking) {
      return {
        ...entry,
        _id: new ObjectId(entry._id),
        hotel: new ObjectId(entry.hotel),
        employee: new ObjectId(entry.employee),
        booking: new ObjectId(entry.booking),
        order: new ObjectId(entry.order),
        createdAt: new Date(entry.createdAt),
        updatedAt: new Date(entry.updatedAt)
      }
    } else {
      return {
        ...entry,
        _id: new ObjectId(entry._id),
        hotel: new ObjectId(entry.hotel),
        employee: new ObjectId(entry.employee),
        order: new ObjectId(entry.order),
        createdAt: new Date(entry.createdAt),
        updatedAt: new Date(entry.updatedAt)
      }
    }
  });
};

const preprocessPaymentData = (data) => {
  return data.map((entry) => {
    if (entry.booking) {
      return {
        ...entry,
        _id: new ObjectId(entry._id),
        hotel: new ObjectId(entry.hotel),
        employee: new ObjectId(entry.employee),
        booking: new ObjectId(entry.booking),
        mode: new ObjectId(entry.mode),
        createdAt: new Date(entry.createdAt),
        updatedAt: new Date(entry.updatedAt)
      }
    } else {
      return {
        ...entry,
        _id: new ObjectId(entry._id),
        hotel: new ObjectId(entry.hotel),
        employee: new ObjectId(entry.employee),
        mode: new ObjectId(entry.mode),
        createdAt: new Date(entry.createdAt),
        updatedAt: new Date(entry.updatedAt)
      }
    }
  });
};

// backup data of mongo db
const backupDB = async (args, req) => {
  const FUNCTION_NAME = "backupDB";
  const hotelId = req.hotelId;
  const employeeId = req.employeeId;

  if (!req.isAuthenticated) throw new Error(ERR_MSG.UNAUTHORIZE, ERR_CODE.FORBIDDEN);

  try {
    const currentDate = new Date().toISOString().slice(0, 10);
    const directoryName = path.join(process.env.APP_DB_BACKUP_PATH, currentDate);
    const outputZipPath = path.join(process.env.APP_DB_BACKUP_PATH, currentDate + ".zip");

    // Create the directory
    await fs.promises.mkdir(directoryName, { recursive: true });

    const uri = `mongodb+srv://${process.env.DB_MONGO_USER}:${process.env.DB_MONGO_PASSWORD}@clubcommcluster.cy8qctn.mongodb.net/${process.env.DB_MONGO_NAME}?retryWrites=true&w=majority`;
    const mongoClient = new MongoClient(uri);
    await mongoClient.connect();
    const db = mongoClient.db(process.env.DB_MONGO_NAME);

    // Get list of collections
    const collections = await db.listCollections().toArray();

    for (const collection of collections) {
      const data = await db.collection(collection.name).find().toArray();

      // Save each collection to a JSON file
      fs.writeFileSync(
        `${directoryName}/${collection.name}.json`,
        JSON.stringify(data, null, 2)
      );
    }

    await mongoClient.close();


    // Create a new zip instance
    const zip = new AdmZip();

    // Add the folder to the zip
    zip.addLocalFolder(directoryName);

    // Save the zip file
    zip.writeZip(outputZipPath);

    // Delete the folder
    fs.rm(directoryName, { recursive: true, force: true }, (error) => {
      if (error) {
        writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
        throw new Error(error.message, ERR_CODE.INTERNAL);
      }
    });

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.DB_BACKUP);

    return true;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new Error(error.message, "");
  }
}

// restore data to mongo db
const restoreDB = async (args, req) => {
  const FUNCTION_NAME = "restoreDB";
  const hotelId = req.hotelId;
  const employeeId = req.employeeId;
  const { fileName } = args.restoreInput;

  if (!req.isAuthenticated) throw new Error(ERR_MSG.UNAUTHORIZE, ERR_CODE.FORBIDDEN);

  try {
    // Specify the zip file and the output folder
    const zipFilePath = path.join(process.env.APP_DB_BACKUP_PATH, fileName);
    const outputFolderPath = path.join(process.env.APP_DB_BACKUP_PATH, fileName.split('.')[0]);

    // Check if the zip file exists
    if (!fs.existsSync(zipFilePath)) {
      return false;
    }

    // Create a new zip instance
    const zip = new AdmZip(zipFilePath);

    // Extract the contents of the zip file
    zip.extractAllTo(outputFolderPath, true);


    const uri = `mongodb+srv://${process.env.DB_MONGO_USER}:${process.env.DB_MONGO_PASSWORD}@clubcommcluster.cy8qctn.mongodb.net/${process.env.DB_MONGO_NAME}?retryWrites=true&w=majority`;
    const mongoClient = new MongoClient(uri);
    await mongoClient.connect();
    const db = mongoClient.db(process.env.DB_MONGO_NAME);

    // Read the backup files from the folder
    const files = fs.readdirSync(outputFolderPath);

    for (const file of files) {
      if (file.endsWith('.json')) {
        const collectionName = file.replace('.json', '');
        const data = JSON.parse(fs.readFileSync(`${outputFolderPath}/${file}`, 'utf8'));

        let formatedData = [];

        if ((collectionName === "roles") ||
          (collectionName === "gsts") ||
          (collectionName === "hotels")) {
          formatedData = preprocessData(data);
        }
        else if ((collectionName === "employees") ||
          (collectionName === "agents") ||
          (collectionName === "plans") ||
          (collectionName === "identifications") ||
          (collectionName === "foods") ||
          (collectionName === "services") ||
          (collectionName === "miscellaneous") ||
          (collectionName === "tables") ||
          (collectionName === "roomcategories") ||
          (collectionName === "paymentmodes")) {
          formatedData = preprocessWithHotelData(data);
        }
        else if ((collectionName === "rooms")) {
          formatedData = preprocessRoomData(data);
        }
        else if ((collectionName === "guests")) {
          formatedData = preprocessGuestData(data);
        }
        else if ((collectionName === "bookings")) {
          formatedData = preprocessBookingData(data);
        }
        else if ((collectionName === "orders")) {
          formatedData = preprocessOrderData(data);
        }
        else if ((collectionName === "bills")) {
          formatedData = preprocessBillData(data);
        }
        else if ((collectionName === "payments")) {
          formatedData = preprocessPaymentData(data);
        }

        if ((collectionName === "roles") ||
          (collectionName === "gsts") ||
          (collectionName === "hotels") ||
          (collectionName === "employees") ||
          (collectionName === "agents") ||
          (collectionName === "plans") ||
          (collectionName === "identifications") ||
          (collectionName === "foods") ||
          (collectionName === "services") ||
          (collectionName === "miscellaneous") ||
          (collectionName === "tables") ||
          (collectionName === "roomcategories") ||
          (collectionName === "paymentmodes") ||
          (collectionName === "rooms") ||
          (collectionName === "guests") ||
          (collectionName === "bookings") ||
          (collectionName === "orders") ||
          (collectionName === "bills") ||
          (collectionName === "payments")) {

          // Clear existing data in the collection (optional)
          await db.collection(collectionName).deleteMany({});

          if (formatedData.length > 0) {
            // Insert the backup data into the collection
            const result = await db.collection(collectionName).insertMany(formatedData);
          }
        }
      }
    }

    await mongoClient.close();

    // Delete the folder
    fs.rm(outputFolderPath, { recursive: true, force: true }, (error) => {
      if (error) {
        writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
        throw new Error(error.message, ERR_CODE.INTERNAL);
      }
    });

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.DB_BACKUP);
    return true;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    return false;
  }
}



// import data from mysql from mongo db
const updateDataWithAI = async (args, req) => {
  const FUNCTION_NAME = "updateDataWithAI";
  const hotelId = "67b2d6bdf5bcd3c22f3a1e41";
  const employeeId = "67ee32c5062049bc74180496";

  let result = false;

  try {
    result = await updateAgentWithAI(hotelId, employeeId);
    result = await updateFoodWithAI(hotelId, employeeId);
    result = await updateMiscellaneousWithAI(hotelId, employeeId);
    result = await updateTableWithAI(hotelId, employeeId);

    return result;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new Error(error.message, "");
    return false;
  }
}


async function updateAgentWithAI(hotelId, employeeId) {
  try {
    const allObjects = await mongoAgents(hotelId, employeeId);

    for (const object of allObjects) {
      const modObject = await mongoAIModAgent(hotelId, employeeId, object._id, object.name, object.description);
    }

    return true;
  } catch (error) {
    console.log(error.message);
    return false;
  }
}

async function updateFoodWithAI(hotelId, employeeId) {
  try {
    const allObjects = await mongoFoods(hotelId, employeeId);

    for (const object of allObjects) {
      const modObject = await mongoAIModFood(hotelId, employeeId, object._id, object.name, object.unitPrice, object.description);
    }

    return true;
  } catch (error) {
    console.log(error.message);
    return false;
  }
}

async function updateMiscellaneousWithAI(hotelId, employeeId) {
  try {
    const allObjects = await mongoMiscellanea(hotelId, employeeId);

    for (const object of allObjects) {
      const modObject = await mongoAIModMiscellaneous(hotelId, employeeId, object._id, object.name, object.unitPrice, object.description);
    }

    return true;
  } catch (error) {
    console.log(error.message);
    return false;
  }
}

async function updateTableWithAI(hotelId, employeeId) {
  try {
    const allObjects = await mongoTables(hotelId, employeeId);

    for (const object of allObjects) {
      const modObject = await mongoAIModTable(hotelId, employeeId, object._id, object.no, object.description);
    }

    return true;
  } catch (error) {
    console.log(error.message);
    return false;
  }
}



// // import data from mysql from mongo db
// const importDB = async (args, req) => {
//   const FUNCTION_NAME = "importDB";
//   //   const hotelId = req.hotelId;
//   //   const employeeId = req.employeeId;

//   //   const connection = mysql.createConnection({
//   //     host: process.env.DB_MYSQL_HOST,
//   //     user: process.env.DB_MYSQL_USER,
//   //     password: process.env.DB_MYSQL_PASSWORD,
//   //     database: process.env.DB_MYSQL_DB
//   //   });

//   //   connection.connect();

//   //   await importGST();
//   //   await importHotel();
//   //   await importEmployee();
//   //   await importIDCard();
//   //   await importPaymentMode();
//   //   await importPlan();
//   //   await importAgent();
//   //   await importRoomCategory();
//   //   await importFood();
//   //   await importMiscellaneous();
//   //   await importTable();
//   //   await importRoom();

//   //   connection.end();


//   // connection.connect(async (error) => {
//   //   if (error) throw new error;

//   //   console.log("MySql connected ...");

//   //   connection.end((error) => {
//   //     if (error) throw new error;

//   //     console.log("MySql disconnected ...");
//   //     writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.DATA_IMPORTED);
//   //     return true;
//   //   });
//   // });


//   //   async function importGST() {
//   //     try {
//   //       const [gstResults] = await connection.promise().query('SELECT * FROM tbl_room_gst');

//   //       // delete all Hotel data
//   //       await GST.deleteMany({});

//   //       for (const o of gstResults) {
//   //         let newData = new GST({
//   //           minTariff: o.dRentMin,
//   //           maxTariff: o.dRentMax,
//   //           iGSTPercentage: o.dCGST,
//   //           sGSTPercentage: o.dSGST
//   //         });

//   //         await newData.save();
//   //       }
//   //     } catch (error) {
//   //       console.log(error.message);
//   //     }
//   //   }

//   //   async function importHotel() {
//   //     try {
//   //       const [hotelResults] = await connection.promise().query('SELECT * FROM tbl_hotel');

//   //       // delete all Hotel data
//   //       await Hotel.deleteMany({});

//   //       for (const o of hotelResults) {
//   //         let newData = new Hotel({
//   //           name: o.sName,
//   //           address: o.sAddress1.trim().toUpperCase(),
//   //           city: o.sLocation.trim().toUpperCase(),
//   //           state: o.sAddress1.split(',')[o.sAddress1.split(',').length - 2].trim().toUpperCase(),
//   //           pin: o.sAddress1.split(',')[o.sAddress1.split(',').length - 1].trim().toUpperCase(),
//   //           phone: o.sPhone,
//   //           email: o.sEmail,
//   //           gstNo: o.sgsts,
//   //           urlName: o.sHotel.trim().toUpperCase(),
//   //           urlPlace: o.sLocation.trim().toUpperCase(),
//   //           serviceChargePercentage: o.dServiceCharge,
//   //           foodIGSTPercentage: o.dRestaurentCGSTCharge,
//   //           foodSGSTPercentage: o.dRestaurentSGSTCharge,
//   //           lastFinalBillNo: o.lLastBillID,
//   //           lastFoodBillNo: o.lLastFoodBillID
//   //         });

//   //         await newData.save();
//   //       }
//   //     } catch (error) {
//   //       console.log(error.message);
//   //     }
//   //   }

//   //   async function importEmployee() {
//   //     try {
//   //       const [employeeResults] = await connection.promise().query('SELECT * FROM tbl_employee');

//   //       await Employee.deleteMany({});

//   //       for (const o of employeeResults) {
//   //         const lHotelID = o.lHotelID;
//   //         const [hotelResults] = await connection.promise().query('SELECT * FROM tbl_hotel WHERE lHotelID = ?', [lHotelID]);
//   //         const hotelName = hotelResults[0].sName;
//   //         const findHotel = await Hotel.findOne({ name: hotelName, isEnable: true });
//   //         const hotelId = findHotel._id;

//   //         const lRoleID = o.lUserLevelID;
//   //         const [roleResults] = await connection.promise().query('SELECT * FROM tbl_access_level WHERE lUserLevelID = ?', [lRoleID]);
//   //         const roleName = roleResults[0].sName;
//   //         const findRole = await Role.findOne({ name: roleName, isEnable: true });

//   //         if (findRole) {
//   //           const roleId = findRole._id;
//   //           const hashedPassword = await bcrypt.hash(o.sPhone, 12);

//   //           let newData = new Employee({
//   //             hotel: hotelId,
//   //             roles: roleId,
//   //             name: o.sName.trim().toUpperCase(),
//   //             address: o.sAddress.trim().toUpperCase(),
//   //             mobile: o.sPhone,
//   //             email: o.sEmail,
//   //             password: hashedPassword
//   //           });

//   //           await newData.save();
//   //         }
//   //       }
//   //     } catch (error) {
//   //       console.log(error.message);
//   //     }
//   //   }

//   //   async function importIDCard() {
//   //     try {
//   //       const [idCardResults] = await connection.promise().query('SELECT * FROM tbl_id_proof');

//   //       // delete all plan data
//   //       await Identification.deleteMany({});

//   //       const hotelResults = await Hotel.find({ isEnable: true }).sort({ name: 1 });

//   //       for (const oh of hotelResults) {
//   //         const hotelId = oh._id;

//   //         for (const o of idCardResults) {
//   //           let newData = new Identification({
//   //             hotel: hotelId,
//   //             name: o.sDocument.trim().toUpperCase()
//   //           });

//   //           await newData.save();
//   //         }
//   //       }
//   //     } catch (error) {
//   //       console.log(error.message);
//   //     }
//   //   }

//   //   async function importAgent() {
//   //     try {
//   //       const [agentResults] = await connection.promise().query('SELECT * FROM tbl_booked_by');

//   //       // delete all agent data
//   //       await Agent.deleteMany({});

//   //       for (const o of agentResults) {
//   //         const lHotelID = o.lHotelID;
//   //         const [hotelResults] = await connection.promise().query('SELECT * FROM tbl_hotel WHERE lHotelID = ?', [lHotelID]);

//   //         const hotelName = hotelResults[0].sName;
//   //         const find = await Hotel.findOne({ name: hotelName, isEnable: true });
//   //         const hotelId = find._id;

//   //         let newData = new Agent({
//   //           hotel: hotelId,
//   //           name: o.sName.trim().toUpperCase(),
//   //           description: o.sDescription
//   //         });

//   //         await newData.save();
//   //       }
//   //     } catch (error) {
//   //       console.log(error.message);
//   //     }
//   //   }

//   //   async function importPlan() {
//   //     try {
//   //       const [planResults] = await connection.promise().query('SELECT * FROM tbl_plan');

//   //       // delete all plan data
//   //       await Plan.deleteMany({});

//   //       for (const o of planResults) {
//   //         const lHotelID = o.lHotelID;
//   //         const [hotelResults] = await connection.promise().query('SELECT * FROM tbl_hotel WHERE lHotelID = ?', [lHotelID]);

//   //         const hotelName = hotelResults[0].sName;
//   //         const find = await Hotel.findOne({ name: hotelName, isEnable: true });
//   //         const hotelId = find._id;

//   //         let newData = new Plan({
//   //           hotel: hotelId,
//   //           name: o.sName.trim().toUpperCase(),
//   //           description: o.sDetail
//   //         });

//   //         await newData.save();
//   //       }
//   //     } catch (error) {
//   //       console.log(error.message);
//   //     }
//   //   }

//   //   async function importPaymentMode() {
//   //     try {
//   //       const [paymentModeResults] = await connection.promise().query('SELECT * FROM tbl_payment_mode');

//   //       // delete all plan data
//   //       await PaymentMode.deleteMany({});

//   //       const hotelResults = await Hotel.find({ isEnable: true }).sort({ name: 1 });

//   //       for (const oh of hotelResults) {
//   //         const hotelId = oh._id;

//   //         for (const o of paymentModeResults) {
//   //           let newData = new PaymentMode({
//   //             hotel: hotelId,
//   //             name: o.sName.trim().toUpperCase()
//   //           });

//   //           await newData.save();
//   //         }
//   //       }
//   //     } catch (error) {
//   //       console.log(error.message);
//   //     }
//   //   }

//   //   async function importRoomCategory() {
//   //     try {
//   //       const [roomCategoryResults] = await connection.promise().query('SELECT * FROM tbl_room_category');

//   //       // delete all room category data
//   //       await RoomCategory.deleteMany({});

//   //       for (const o of roomCategoryResults) {
//   //         const lHotelID = o.lHotelID;
//   //         const [hotelResults] = await connection.promise().query('SELECT * FROM tbl_hotel WHERE lHotelID = ?', [lHotelID]);

//   //         const hotelName = hotelResults[0].sName;
//   //         const find = await Hotel.findOne({ name: hotelName, isEnable: true });
//   //         const hotelId = find._id;

//   //         let newData = new RoomCategory({
//   //           hotel: hotelId,
//   //           name: o.sName.trim().toUpperCase(),
//   //           accommodation: 2,
//   //           tariff: o.dRoomCharge,
//   //           maxDiscount: o.dMaxDiscount,
//   //           extraBedTariff: o.dExtraBedCharge,
//   //           extraPersonTariff: o.dExtraPersonCharge
//   //         });

//   //         await newData.save();
//   //       }
//   //     } catch (error) {
//   //       console.log(error.message);
//   //     }
//   //   }

//   //   async function importFood() {
//   //     try {
//   //       const [foodResults] = await connection.promise().query('SELECT * FROM tbl_meal');

//   //       // delete all room category data
//   //       await Food.deleteMany({});

//   //       for (const o of foodResults) {
//   //         const lHotelID = o.lHotelID;
//   //         const [hotelResults] = await connection.promise().query('SELECT * FROM tbl_hotel WHERE lHotelID = ?', [lHotelID]);

//   //         const hotelName = hotelResults[0].sName;
//   //         const find = await Hotel.findOne({ name: hotelName, isEnable: true });
//   //         const hotelId = find._id;

//   //         let newData = new Food({
//   //           hotel: hotelId,
//   //           name: o.sName.trim().toUpperCase(),
//   //           unitPrice: o.dRate
//   //         });

//   //         await newData.save();
//   //       }
//   //     } catch (error) {
//   //       console.log(error.message);
//   //     }
//   //   }

//   //   async function importMiscellaneous() {
//   //     try {
//   //       const [miscellaneousResults] = await connection.promise().query('SELECT * FROM tbl_product');

//   //       // delete all room category data
//   //       await Miscellaneous.deleteMany({});

//   //       for (const o of miscellaneousResults) {
//   //         const lHotelID = o.lHotelID;
//   //         const [hotelResults] = await connection.promise().query('SELECT * FROM tbl_hotel WHERE lHotelID = ?', [lHotelID]);

//   //         const hotelName = hotelResults[0].sName;
//   //         const find = await Hotel.findOne({ name: hotelName, isEnable: true });
//   //         const hotelId = find._id;

//   //         let newData = new Miscellaneous({
//   //           hotel: hotelId,
//   //           name: o.sName.trim().toUpperCase(),
//   //           unitPrice: o.dRate
//   //         });

//   //         await newData.save();
//   //       }
//   //     } catch (error) {
//   //       console.log(error.message);
//   //     }
//   //   }

//   //   async function importTable() {
//   //     try {
//   //       const [tableResults] = await connection.promise().query('SELECT * FROM tbl_table');

//   //       // delete all room category data
//   //       await Table.deleteMany({});

//   //       for (const o of tableResults) {
//   //         const lHotelID = o.lHotelID;
//   //         const [hotelResults] = await connection.promise().query('SELECT * FROM tbl_hotel WHERE lHotelID = ?', [lHotelID]);

//   //         const hotelName = hotelResults[0].sName;
//   //         const find = await Hotel.findOne({ name: hotelName, isEnable: true });
//   //         const hotelId = find._id;

//   //         let newData = new Table({
//   //           hotel: hotelId,
//   //           no: o.sTableNo.trim().toUpperCase(),
//   //           accommodation: 2
//   //         });

//   //         await newData.save();
//   //       }
//   //     } catch (error) {
//   //       console.log(error.message);
//   //     }
//   //   }

//   //   async function importRoom() {
//   //     try {
//   //       const [roomResults] = await connection.promise().query('SELECT * FROM tbl_room');

//   //       // delete all room category data
//   //       await Room.deleteMany({});

//   //       for (const o of roomResults) {
//   //         const lHotelID = o.lHotelID;
//   //         const [hotelResults] = await connection.promise().query('SELECT * FROM tbl_hotel WHERE lHotelID = ?', [lHotelID]);

//   //         const hotelName = hotelResults[0].sName;
//   //         const findHotel = await Hotel.findOne({ name: hotelName, isEnable: true });
//   //         const hotelId = findHotel._id;

//   //         const lRoomCategoryID = o.lRoomCategoryID;
//   //         const [roomCategoryResults] = await connection.promise().query('SELECT * FROM tbl_room_category WHERE lHotelID = ? AND lRoomCategoryID = ?', [lHotelID, lRoomCategoryID]);

//   //         const roomCategoryName = roomCategoryResults[0].sName;
//   //         const findRoomCategory = await RoomCategory.findOne({ hotel: hotelId, name: roomCategoryName, isEnable: true });

//   //         if (findRoomCategory) {
//   //           const roomCategoryId = findRoomCategory._id;

//   //           let newData = new Room({
//   //             hotel: hotelId,
//   //             category: roomCategoryId,
//   //             no: o.sRoomNo.trim().toUpperCase(),
//   //             accommodation: 2,
//   //             tariff: o.dRoomCharge,
//   //             extraBedTariff: o.dExtraBedCharge,
//   //             extraPersonTariff: o.dExtraPersonCharge,
//   //             maxDiscount: o.dMaxDiscount
//   //           });

//   //           await newData.save();
//   //         }
//   //       }
//   //     } catch (error) {
//   //       console.log(error.message);
//   //     }
//   //   }

//   return true;
// };

module.exports = {
  backupDB,
  restoreDB,
  updateDataWithAI
  // importDB,
};