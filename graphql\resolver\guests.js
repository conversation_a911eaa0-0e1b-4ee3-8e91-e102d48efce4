const Identification = require("../../models/identification");
const { mongoGuests, mongoSearchGuest, mongoGetGuest, mongoAddGuest, mongoModGuest, mongoDelGuest } = require("./mongo/guestMongo");
const { guestDetail } = require("../../helpers/db");
const { ACTION_LIST, RESOURCE_LIST, isAuthorized } = require("../../helpers/permissionOptions");
const { customError } = require('../../helpers/customError');
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { ACTIVITY_LIST } = require("../../helpers/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../helpers/messageOptions");
const FILE_NAME = "guests.js";

// get all documents of a collection
const guests = async (args, req) => {
  const FUNCTION_NAME = "guests";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.GUEST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    const getObject = await mongoGuests(hotelId, employeeId);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.GUEST_LIST);

    // return output
    return getObject.map(async (object) => {
      return await guestDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchGuest = async (args, req) => {
  const FUNCTION_NAME = "searchGuest";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.guestSearchInput ? args.guestSearchInput.trim().toUpperCase() : "";

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.GUEST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // search data
    const searchObjects = await mongoSearchGuest(hotelId, employeeId, searchKey);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.GUEST_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await guestDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const getGuest = async (args, req) => {
  const FUNCTION_NAME = "getGuest";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.VIEW, RESOURCE_LIST.GUEST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // get data
    const getObject = await mongoGetGuest(hotelId, employeeId, _id);
    if (!getObject) throw new customError(ERR_MSG.GUEST_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.GUEST_GET);

    // return output
    return await guestDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addGuest = async (args, req) => {
  const FUNCTION_NAME = "addGuest";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { name, address, city, policeStation, state, pin, mobile, email,
    father, age, guestCount, maleCount, femaleCount, identification, idNo,
    company, companyAddress, companyGstNo } = args.guestInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.CREATE, RESOURCE_LIST.GUEST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (name === "") throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);
    if (address === "") throw new customError(ERR_MSG.INVALID_ADDRESS, ERR_CODE.BAD_REQUEST);
    if (city === "") throw new customError(ERR_MSG.INVALID_CITY, ERR_CODE.BAD_REQUEST);
    if (policeStation === "") throw new customError(ERR_MSG.INVALID_PS, ERR_CODE.BAD_REQUEST);
    if (state === "") throw new customError(ERR_MSG.INVALID_STATE, ERR_CODE.BAD_REQUEST);
    if (pin === "") throw new customError(ERR_MSG.INVALID_PIN, ERR_CODE.BAD_REQUEST);
    if (mobile === "") throw new customError(ERR_MSG.INVALID_MOBILE, ERR_CODE.BAD_REQUEST);
    if (email === "") throw new customError(ERR_MSG.INVALID_EMAIL, ERR_CODE.BAD_REQUEST);
    if (father === "") throw new customError(ERR_MSG.INVALID_FATHER, ERR_CODE.BAD_REQUEST);
    if (age <= 0) throw new customError(ERR_MSG.INVALID_AGE, ERR_CODE.BAD_REQUEST);
    if (guestCount <= 0) throw new customError(ERR_MSG.INVALID_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (maleCount < 0) throw new customError(ERR_MSG.INVALID_MALE_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (femaleCount < 0) throw new customError(ERR_MSG.INVALID_FEMALE_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (identification === "") throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);
    if (idNo === "") throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // check for identification
    const identificationCondition = { hotel: hotelId, _id: identification, isEnable: true };
    const identificationCursor = await Identification.findOne(identificationCondition);
    if (!identificationCursor) throw new customError(ERR_MSG.IDENTIFICATION_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    const _name = name.trim().toUpperCase();
    const _address = address.trim().toUpperCase();
    const _city = city.trim().toUpperCase();
    const _policeStation = policeStation.trim().toUpperCase();
    const _state = state.trim().toUpperCase();
    const _pin = pin.trim();
    const _mobile = mobile.trim();
    const _email = email.trim().toLowerCase();
    const _father = father.trim().toUpperCase();
    const _age = age;
    const _guestCount = guestCount;
    const _maleCount = maleCount;
    const _femaleCount = femaleCount;
    const _identification = identification.trim();
    const _idNo = idNo.trim().toUpperCase();
    const _company = company.trim().toUpperCase();
    const _companyAddress = companyAddress.trim().toUpperCase();
    const _companyGstNo = companyGstNo.trim().toUpperCase();

    // add data
    const addObject = await mongoAddGuest(hotelId, employeeId, _name, _address, _city, _policeStation, _state,
      _pin, _mobile, _email, _father, _age, _guestCount, _maleCount, _femaleCount, _identification, _idNo,
      _company, _companyAddress, _companyGstNo);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.GUEST_ADD);

    // return output
    return await guestDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modGuest = async (args, req) => {
  const FUNCTION_NAME = "modGuest";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, name, address, city, policeStation, state, pin, mobile, email,
    father, age, guestCount, maleCount, femaleCount, identification, idNo,
    company, companyAddress, companyGstNo } = args.guestInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.EDIT, RESOURCE_LIST.GUEST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (name === "") throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);
    if (address === "") throw new customError(ERR_MSG.INVALID_ADDRESS, ERR_CODE.BAD_REQUEST);
    if (city === "") throw new customError(ERR_MSG.INVALID_CITY, ERR_CODE.BAD_REQUEST);
    if (policeStation === "") throw new customError(ERR_MSG.INVALID_PS, ERR_CODE.BAD_REQUEST);
    if (state === "") throw new customError(ERR_MSG.INVALID_STATE, ERR_CODE.BAD_REQUEST);
    if (pin === "") throw new customError(ERR_MSG.INVALID_PIN, ERR_CODE.BAD_REQUEST);
    if (mobile === "") throw new customError(ERR_MSG.INVALID_MOBILE, ERR_CODE.BAD_REQUEST);
    if (email === "") throw new customError(ERR_MSG.INVALID_EMAIL, ERR_CODE.BAD_REQUEST);
    if (father === "") throw new customError(ERR_MSG.INVALID_FATHER, ERR_CODE.BAD_REQUEST);
    if (age <= 0) throw new customError(ERR_MSG.INVALID_AGE, ERR_CODE.BAD_REQUEST);
    if (guestCount <= 0) throw new customError(ERR_MSG.INVALID_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (maleCount < 0) throw new customError(ERR_MSG.INVALID_MALE_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (femaleCount < 0) throw new customError(ERR_MSG.INVALID_FEMALE_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (identification === "") throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);
    if (idNo === "") throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    const _name = name.trim().toUpperCase();
    const _address = address.trim().toUpperCase();
    const _city = city.trim().toUpperCase();
    const _policeStation = policeStation.trim().toUpperCase();
    const _state = state.trim().toUpperCase();
    const _pin = pin.trim();
    const _mobile = mobile.trim();
    const _email = email.trim().toLowerCase();
    const _father = father.trim().toUpperCase();
    const _age = age;
    const _guestCount = guestCount;
    const _maleCount = maleCount;
    const _femaleCount = femaleCount;
    const _identification = identification.trim();
    const _idNo = idNo.trim().toUpperCase();
    const _company = company.trim().toUpperCase();
    const _companyAddress = companyAddress.trim().toUpperCase();
    const _companyGstNo = companyGstNo.trim().toUpperCase();

    // check for identification
    const identificationCondition = { hotel: hotelId, _id: identification, isEnable: true };
    const identificationCursor = await Identification.findOne(identificationCondition);
    if (!identificationCursor) throw new customError(ERR_MSG.IDENTIFICATION_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // modify data
    const modObject = await mongoModGuest(hotelId, employeeId, _id, _name, _address, _city, _policeStation, _state,
      _pin, _mobile, _email, _father, _age, _guestCount, _maleCount, _femaleCount, _identification, _idNo,
      _company, _companyAddress, _companyGstNo);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.GUEST_MOD);

    // return output
    return await guestDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delGuest = async (args, req) => {
  const FUNCTION_NAME = "delGuest";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, ACTION_LIST.REMOVE, RESOURCE_LIST.GUEST)) throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelGuest(hotelId, employeeId, _id);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.GUEST_DEL);

    // return output
    return await guestDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

module.exports = { guests, searchGuest, getGuest, addGuest, modGuest, delGuest };