const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../messageOptions");

// Function to get hotel data from Redis
const getOneHotelRedis = async (id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_HOTEL}:${id}`);

        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all hotel data from Redis
const getAllHotelRedis = async () => {
    try {
        const prefix = `${process.env.HASH_HOTEL}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);
        const parsedData = await Promise.all(
            hashKeys.map(async (hashKey) => {
                return await readHashValues(hashKey);
            })
        );
        const sortedByName = parsedData.sort((a, b) => a.name.localeCompare(b.name));

        return sortedByName;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set hotel data in Redis
const setOneHotelRedis = async (data) => {
    try {
        if (data) {
            await delOneHotelRedis(data._id);

            await addHashValues(`${process.env.HASH_HOTEL}:${data._id}`,
                {
                    _id: data._id,
                    name: data.name,
                    address: data.address,
                    city: data.city,
                    state: data.state,
                    pin: data.pin,
                    phone: data.phone,
                    email: data.email,
                    gstNo: data.gstNo,
                    urlName: data.urlName,
                    urlPlace: data.urlPlace,
                    foodIGSTPercentage: data.foodIGSTPercentage,
                    foodSGSTPercentage: data.foodSGSTPercentage,
                    serviceIGSTPercentage: data.serviceIGSTPercentage,
                    serviceSGSTPercentage: data.serviceSGSTPercentage,
                    serviceChargePercentage: data.serviceChargePercentage,
                    lastKOTNo: data.lastKOTNo,
                    lastSOTNo: data.lastSOTNo,
                    lastMOTNo: data.lastMOTNo,
                    lastFinalBillNo: data.lastFinalBillNo,
                    lastFoodBillNo: data.lastFoodBillNo,
                    lastServiceBillNo: data.lastServiceBillNo,
                    lastMiscellaneousBillNo: data.lastMiscellaneousBillNo,
                    lastReceiptNo: data.lastReceiptNo,
                    isEnable: data.isEnable,
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt
                }
            )
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set hotel data in Redis
const setAllHotelRedis = async (dataArray) => {
    try {
        if (dataArray) {
            await delAllHotelRedis();

            dataArray.map(async (data) => {
                await addHashValues(`${process.env.HASH_HOTEL}:${data._id}`,
                    {
                        _id: data._id,
                        name: data.name,
                        address: data.address,
                        city: data.city,
                        state: data.state,
                        pin: data.pin,
                        phone: data.phone,
                        email: data.email,
                        gstNo: data.gstNo,
                        urlName: data.urlName,
                        urlPlace: data.urlPlace,
                        foodIGSTPercentage: data.foodIGSTPercentage,
                        foodSGSTPercentage: data.foodSGSTPercentage,
                        serviceIGSTPercentage: data.serviceIGSTPercentage,
                        serviceSGSTPercentage: data.serviceSGSTPercentage,
                        serviceChargePercentage: data.serviceChargePercentage,
                        lastKOTNo: data.lastKOTNo,
                        lastSOTNo: data.lastSOTNo,
                        lastMOTNo: data.lastMOTNo,
                        lastFinalBillNo: data.lastFinalBillNo,
                        lastFoodBillNo: data.lastFoodBillNo,
                        lastServiceBillNo: data.lastServiceBillNo,
                        lastMiscellaneousBillNo: data.lastMiscellaneousBillNo,
                        lastReceiptNo: data.lastReceiptNo,
                        isEnable: data.isEnable,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                )
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete agent data from Redis
const delOneHotelRedis = async (id) => {
    try {
        await delHashKey(`${process.env.HASH_HOTEL}:${id}`);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete hotel data from Redis
const delAllHotelRedis = async () => {
    try {
        const prefix = `${process.env.HASH_HOTEL}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);

        if (hashKeys) {
            await Promise.all(
                hashKeys.map(async (hashKey) => {
                    await delHashKey(hashKey)
                })
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

module.exports = {
    getOneHotelRedis,
    getAllHotelRedis,
    setOneHotelRedis,
    setAllHotelRedis,
    delOneHotelRedis,
    delAllHotelRedis
};